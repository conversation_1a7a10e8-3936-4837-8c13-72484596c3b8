<template>
  <div class="search-page">
    <div class="search-header">
      <div class="search-input-wrapper">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索项目、审计、漏洞..."
          size="large"
          :loading="searching"
          @search="handleSearch"
          @pressEnter="handleSearch"
          style="max-width: 600px"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input-search>
      </div>
      
      <div v-if="searchKeyword" class="search-meta">
        <span class="search-results-count">
          找到 <strong>{{ totalResults }}</strong> 个结果，用时 {{ searchTime }}ms
        </span>
      </div>
    </div>
    
    <div class="search-content">
      <a-row :gutter="[24, 24]">
        <!-- 搜索过滤器 -->
        <a-col :xs="24" :lg="6">
          <a-card title="筛选条件" size="small">
            <div class="filter-section">
              <div class="filter-title">类型</div>
              <a-checkbox-group v-model:value="filters.types" @change="handleFilterChange">
                <div class="filter-options">
                  <a-checkbox value="project">
                    项目 <span class="count">({{ getTypeCount('project') }})</span>
                  </a-checkbox>
                  <a-checkbox value="audit">
                    审计 <span class="count">({{ getTypeCount('audit') }})</span>
                  </a-checkbox>
                  <a-checkbox value="vulnerability">
                    漏洞 <span class="count">({{ getTypeCount('vulnerability') }})</span>
                  </a-checkbox>
                  <a-checkbox value="report">
                    报告 <span class="count">({{ getTypeCount('report') }})</span>
                  </a-checkbox>
                </div>
              </a-checkbox-group>
            </div>
            
            <div class="filter-section">
              <div class="filter-title">严重程度</div>
              <a-checkbox-group v-model:value="filters.severities" @change="handleFilterChange">
                <div class="filter-options">
                  <a-checkbox value="critical">
                    严重 <span class="count">({{ getSeverityCount('critical') }})</span>
                  </a-checkbox>
                  <a-checkbox value="high">
                    高危 <span class="count">({{ getSeverityCount('high') }})</span>
                  </a-checkbox>
                  <a-checkbox value="medium">
                    中危 <span class="count">({{ getSeverityCount('medium') }})</span>
                  </a-checkbox>
                  <a-checkbox value="low">
                    低危 <span class="count">({{ getSeverityCount('low') }})</span>
                  </a-checkbox>
                </div>
              </a-checkbox-group>
            </div>
            
            <div class="filter-section">
              <div class="filter-title">时间范围</div>
              <a-range-picker
                v-model:value="filters.dateRange"
                @change="handleFilterChange"
                style="width: 100%"
                size="small"
              />
            </div>
            
            <div class="filter-actions">
              <a-button size="small" @click="clearFilters">清空筛选</a-button>
            </div>
          </a-card>
        </a-col>
        
        <!-- 搜索结果 -->
        <a-col :xs="24" :lg="18">
          <div class="search-results">
            <!-- 排序选项 -->
            <div class="search-toolbar">
              <a-space>
                <span>排序：</span>
                <a-select
                  v-model:value="sortBy"
                  @change="handleSortChange"
                  size="small"
                  style="width: 120px"
                >
                  <a-select-option value="relevance">相关性</a-select-option>
                  <a-select-option value="created_at">创建时间</a-select-option>
                  <a-select-option value="updated_at">更新时间</a-select-option>
                </a-select>
                
                <a-select
                  v-model:value="sortOrder"
                  @change="handleSortChange"
                  size="small"
                  style="width: 80px"
                >
                  <a-select-option value="desc">降序</a-select-option>
                  <a-select-option value="asc">升序</a-select-option>
                </a-select>
              </a-space>
            </div>
            
            <!-- 结果列表 -->
            <div class="results-list">
              <div v-if="searching" class="loading-container">
                <a-spin size="large" />
              </div>
              
              <div v-else-if="searchResults.length === 0 && searchKeyword" class="no-results">
                <a-empty description="未找到相关结果">
                  <template #image>
                    <SearchOutlined style="font-size: 64px; color: #ccc;" />
                  </template>
                </a-empty>
                <div class="search-suggestions">
                  <h4>搜索建议：</h4>
                  <ul>
                    <li>检查关键词拼写</li>
                    <li>尝试使用更通用的关键词</li>
                    <li>减少关键词数量</li>
                    <li>调整筛选条件</li>
                  </ul>
                </div>
              </div>
              
              <div v-else>
                <div 
                  v-for="result in searchResults" 
                  :key="result.id"
                  class="result-item"
                  @click="handleResultClick(result)"
                >
                  <div class="result-header">
                    <div class="result-icon">
                      <component :is="getResultIcon(result.type)" />
                    </div>
                    <div class="result-meta">
                      <a-tag :color="getTypeColor(result.type)" size="small">
                        {{ getTypeText(result.type) }}
                      </a-tag>
                      <a-tag 
                        v-if="result.severity" 
                        :color="getSeverityColor(result.severity)" 
                        size="small"
                      >
                        {{ result.severity }}
                      </a-tag>
                    </div>
                  </div>
                  
                  <div class="result-content">
                    <h3 class="result-title" v-html="highlightKeyword(result.title)"></h3>
                    <p class="result-description" v-html="highlightKeyword(result.description)"></p>
                    
                    <div class="result-highlights" v-if="result.highlight">
                      <div v-if="result.highlight.content" class="highlight-section">
                        <strong>相关内容：</strong>
                        <div 
                          v-for="(content, index) in result.highlight.content.slice(0, 2)" 
                          :key="index"
                          class="highlight-text"
                          v-html="content"
                        ></div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="result-footer">
                    <span class="result-time">
                      {{ formatDate(result.updated_at) }}
                    </span>
                  </div>
                </div>
                
                <!-- 分页 -->
                <div class="pagination-wrapper">
                  <a-pagination
                    v-model:current="pagination.current"
                    v-model:page-size="pagination.pageSize"
                    :total="totalResults"
                    :show-size-changer="true"
                    :show-quick-jumper="true"
                    :show-total="(total) => `共 ${total} 条结果`"
                    @change="handlePageChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { searchApi } from '@/api/search'
import { formatDate } from '@/utils/date'
import {
  SearchOutlined,
  ProjectOutlined,
  BugOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import type { SearchResult } from '@/api/search'
import type { Dayjs } from 'dayjs'

const route = useRoute()
const router = useRouter()

const searchKeyword = ref('')
const searching = ref(false)
const searchResults = ref<SearchResult[]>([])
const totalResults = ref(0)
const searchTime = ref(0)

const sortBy = ref('relevance')
const sortOrder = ref('desc')

const filters = reactive({
  types: [] as string[],
  severities: [] as string[],
  dateRange: null as [Dayjs, Dayjs] | null
})

const pagination = reactive({
  current: 1,
  pageSize: 10
})

const aggregations = ref<{
  types: Array<{ key: string; count: number }>
  severities: Array<{ key: string; count: number }>
}>({
  types: [],
  severities: []
})

// 获取类型数量
const getTypeCount = (type: string) => {
  const item = aggregations.value.types.find(t => t.key === type)
  return item?.count || 0
}

// 获取严重程度数量
const getSeverityCount = (severity: string) => {
  const item = aggregations.value.severities.find(s => s.key === severity)
  return item?.count || 0
}

// 获取结果图标
const getResultIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    project: ProjectOutlined,
    audit: BugOutlined,
    vulnerability: ExclamationCircleOutlined,
    report: FileTextOutlined
  }
  return iconMap[type] || SearchOutlined
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    project: 'blue',
    audit: 'green',
    vulnerability: 'orange',
    report: 'purple'
  }
  return colorMap[type] || 'default'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    project: '项目',
    audit: '审计',
    vulnerability: '漏洞',
    report: '报告'
  }
  return textMap[type] || type
}

// 获取严重程度颜色
const getSeverityColor = (severity: string) => {
  const colorMap: Record<string, string> = {
    critical: 'red',
    high: 'orange',
    medium: 'yellow',
    low: 'green'
  }
  return colorMap[severity] || 'default'
}

// 高亮关键词
const highlightKeyword = (text: string) => {
  if (!searchKeyword.value || !text) return text
  
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 执行搜索
const performSearch = async () => {
  if (!searchKeyword.value.trim()) return
  
  searching.value = true
  try {
    const params = {
      keyword: searchKeyword.value.trim(),
      limit: pagination.pageSize,
      offset: (pagination.current - 1) * pagination.pageSize,
      sort_by: sortBy.value,
      sort_order: sortOrder.value as 'asc' | 'desc'
    }
    
    const response = await searchApi.globalSearch(params)
    
    searchResults.value = response.data.results
    totalResults.value = response.data.total
    searchTime.value = response.data.took
    aggregations.value = response.data.aggregations || { types: [], severities: [] }
  } catch (error) {
    console.error('Search error:', error)
    searchResults.value = []
    totalResults.value = 0
  } finally {
    searching.value = false
  }
}

// 处理搜索
const handleSearch = (value?: string) => {
  if (value !== undefined) {
    searchKeyword.value = value
  }
  
  pagination.current = 1
  performSearch()
  
  // 更新URL
  router.replace({
    query: { 
      ...route.query, 
      q: searchKeyword.value,
      page: 1
    }
  })
}

// 处理筛选变化
const handleFilterChange = () => {
  pagination.current = 1
  performSearch()
}

// 处理排序变化
const handleSortChange = () => {
  pagination.current = 1
  performSearch()
}

// 处理分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page
  pagination.pageSize = pageSize
  performSearch()
  
  // 更新URL
  router.replace({
    query: { 
      ...route.query, 
      page: page > 1 ? page : undefined
    }
  })
}

// 处理结果点击
const handleResultClick = (result: SearchResult) => {
  router.push(result.url)
}

// 清空筛选
const clearFilters = () => {
  filters.types = []
  filters.severities = []
  filters.dateRange = null
  handleFilterChange()
}

// 监听路由变化
watch(() => route.query, (query) => {
  if (query.q && query.q !== searchKeyword.value) {
    searchKeyword.value = query.q as string
    pagination.current = query.page ? Number(query.page) : 1
    performSearch()
  }
}, { immediate: true })

onMounted(() => {
  // 从URL获取搜索关键词
  if (route.query.q) {
    searchKeyword.value = route.query.q as string
    pagination.current = route.query.page ? Number(route.query.page) : 1
    performSearch()
  }
})
</script>

<style scoped>
.search-page {
  padding: 0;
}

.search-header {
  background: white;
  padding: 24px;
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-input-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.search-meta {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.search-results-count strong {
  color: #1890ff;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #262626;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-options .ant-checkbox-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.count {
  color: #999;
  font-size: 12px;
}

.filter-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.search-toolbar {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.results-list {
  min-height: 400px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
}

.search-suggestions {
  margin-top: 24px;
  text-align: left;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.search-suggestions h4 {
  margin-bottom: 12px;
  color: #262626;
}

.search-suggestions ul {
  color: #666;
  font-size: 14px;
}

.search-suggestions li {
  margin-bottom: 4px;
}

.result-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.2s;
}

.result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-icon {
  font-size: 16px;
  color: #666;
}

.result-meta {
  display: flex;
  gap: 8px;
}

.result-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #1890ff;
}

.result-description {
  color: #666;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.result-highlights {
  margin-bottom: 12px;
}

.highlight-section {
  margin-bottom: 8px;
}

.highlight-text {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 4px 0;
  font-size: 12px;
  line-height: 1.4;
}

.result-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #999;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

:deep(mark) {
  background: #fff2b8;
  padding: 0 2px;
  border-radius: 2px;
}

/* 暗黑模式适配 */
[data-theme='dark'] .search-header {
  background: #1f1f1f;
  border: 1px solid #303030;
}

[data-theme='dark'] .result-item {
  background: #1f1f1f;
  border: 1px solid #303030;
}

[data-theme='dark'] .result-title {
  color: #177ddc;
}

[data-theme='dark'] .result-description {
  color: #a6a6a6;
}

[data-theme='dark'] .highlight-text {
  background: #262626;
}

[data-theme='dark'] .filter-title {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .filter-actions {
  border-top-color: #303030;
}

[data-theme='dark'] .search-toolbar {
  border-bottom-color: #303030;
}

[data-theme='dark'] :deep(mark) {
  background: #614700;
  color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-header {
    padding: 16px;
  }
  
  .result-item {
    padding: 16px;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
