<template>
  <div 
    ref="chartRef" 
    :class="['base-chart', { 'chart-loading': loading }]"
    :style="{ width, height }"
  >
    <div v-if="loading" class="chart-loading-overlay">
      <a-spin size="large" />
    </div>
    <div v-if="error" class="chart-error">
      <a-result
        status="error"
        :title="error"
        :sub-title="errorDesc"
      >
        <template #extra>
          <a-button @click="handleRetry">重试</a-button>
        </template>
      </a-result>
    </div>
    <div v-if="!data || (Array.isArray(data) && data.length === 0)" class="chart-empty">
      <a-empty :description="emptyText" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useThemeStore } from '@/stores/themeStore'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'

interface Props {
  option: EChartsOption
  data?: any
  loading?: boolean
  error?: string
  errorDesc?: string
  emptyText?: string
  width?: string
  height?: string
  autoResize?: boolean
  theme?: 'light' | 'dark' | 'auto'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: '',
  errorDesc: '图表加载失败，请稍后重试',
  emptyText: '暂无数据',
  width: '100%',
  height: '400px',
  autoResize: true,
  theme: 'auto'
})

const emit = defineEmits<{
  retry: []
  chartReady: [chart: ECharts]
  chartClick: [params: any]
  chartHover: [params: any]
}>()

const themeStore = useThemeStore()
const chartRef = ref<HTMLElement>()
let chartInstance: ECharts | null = null
let resizeObserver: ResizeObserver | null = null

// 获取当前主题
const getCurrentTheme = () => {
  if (props.theme === 'auto') {
    return themeStore.isDarkMode ? 'dark' : 'light'
  }
  return props.theme
}

// 获取主题配置
const getThemeConfig = () => {
  const isDark = getCurrentTheme() === 'dark'
  
  return {
    backgroundColor: isDark ? '#1f1f1f' : '#ffffff',
    textStyle: {
      color: isDark ? '#ffffff' : '#333333'
    },
    grid: {
      borderColor: isDark ? '#484848' : '#e0e0e0'
    },
    categoryAxis: {
      axisLine: {
        lineStyle: {
          color: isDark ? '#484848' : '#e0e0e0'
        }
      },
      axisTick: {
        lineStyle: {
          color: isDark ? '#484848' : '#e0e0e0'
        }
      },
      axisLabel: {
        color: isDark ? '#ffffff' : '#333333'
      },
      splitLine: {
        lineStyle: {
          color: isDark ? '#484848' : '#e0e0e0'
        }
      }
    },
    valueAxis: {
      axisLine: {
        lineStyle: {
          color: isDark ? '#484848' : '#e0e0e0'
        }
      },
      axisTick: {
        lineStyle: {
          color: isDark ? '#484848' : '#e0e0e0'
        }
      },
      axisLabel: {
        color: isDark ? '#ffffff' : '#333333'
      },
      splitLine: {
        lineStyle: {
          color: isDark ? '#484848' : '#e0e0e0'
        }
      }
    },
    legend: {
      textStyle: {
        color: isDark ? '#ffffff' : '#333333'
      }
    },
    tooltip: {
      backgroundColor: isDark ? '#303030' : '#ffffff',
      borderColor: isDark ? '#484848' : '#e0e0e0',
      textStyle: {
        color: isDark ? '#ffffff' : '#333333'
      }
    }
  }
}

// 合并配置
const mergeOption = (option: EChartsOption): EChartsOption => {
  const themeConfig = getThemeConfig()
  
  return {
    ...themeConfig,
    ...option,
    textStyle: {
      ...themeConfig.textStyle,
      ...option.textStyle
    },
    grid: {
      ...themeConfig.grid,
      ...option.grid
    },
    tooltip: {
      ...themeConfig.tooltip,
      ...option.tooltip
    },
    legend: {
      ...themeConfig.legend,
      ...option.legend
    }
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value || props.loading || props.error) return
  
  await nextTick()
  
  try {
    // 销毁现有实例
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    
    // 创建新实例
    chartInstance = echarts.init(chartRef.value, getCurrentTheme())
    
    // 设置配置
    const mergedOption = mergeOption(props.option)
    chartInstance.setOption(mergedOption, true)
    
    // 绑定事件
    chartInstance.on('click', (params) => {
      emit('chartClick', params)
    })
    
    chartInstance.on('mouseover', (params) => {
      emit('chartHover', params)
    })
    
    // 触发就绪事件
    emit('chartReady', chartInstance)
    
    // 设置自动调整大小
    if (props.autoResize) {
      setupResize()
    }
  } catch (error) {
    console.error('Chart initialization error:', error)
  }
}

// 设置自动调整大小
const setupResize = () => {
  if (!chartRef.value || !chartInstance) return
  
  // 使用ResizeObserver监听容器大小变化
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        chartInstance.resize()
      }
    })
    resizeObserver.observe(chartRef.value)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || props.loading || props.error) return
  
  try {
    const mergedOption = mergeOption(props.option)
    chartInstance.setOption(mergedOption, true)
  } catch (error) {
    console.error('Chart update error:', error)
  }
}

// 重试
const handleRetry = () => {
  emit('retry')
}

// 清理资源
const cleanup = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  
  window.removeEventListener('resize', handleResize)
  
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听配置变化
watch(() => props.option, updateChart, { deep: true })

// 监听主题变化
watch(() => themeStore.currentTheme, () => {
  initChart()
})

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (!loading && !props.error) {
    nextTick(() => {
      initChart()
    })
  }
})

// 监听错误状态
watch(() => props.error, (error) => {
  if (error && chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

onMounted(() => {
  if (!props.loading && !props.error) {
    initChart()
  }
})

onUnmounted(() => {
  cleanup()
})

// 暴露方法给父组件
defineExpose({
  getChart: () => chartInstance,
  resize: () => chartInstance?.resize(),
  refresh: initChart
})
</script>

<style scoped>
.base-chart {
  position: relative;
  overflow: hidden;
}

.chart-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

[data-theme='dark'] .chart-loading-overlay {
  background: rgba(31, 31, 31, 0.8);
}

.chart-error,
.chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.chart-loading {
  pointer-events: none;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .base-chart {
    height: 300px !important;
  }
}

@media (max-width: 576px) {
  .base-chart {
    height: 250px !important;
  }
}
</style>
