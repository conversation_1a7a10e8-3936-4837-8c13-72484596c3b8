import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'

const routes: RouteRecordRaw[] = [
  // 用户认证路由
  {
    path: '/user/login',
    name: 'Login',
    component: () => import('@/views/User/Login.vue'),
    meta: { requiresAuth: false, title: '登录' }
  },
  {
    path: '/user/register',
    name: 'Register',
    component: () => import('@/views/User/Register.vue'),
    meta: { requiresAuth: false, title: '注册' }
  },
  // 直接登录路由
  {
    path: '/login',
    name: 'DirectLogin',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false, title: '登录' }
  },
  // 主应用路由（使用主布局）
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { title: '仪表板', requiresAuth: true }
  },
  {
    path: '/project',
    name: 'ProjectIndex',
    component: () => import('@/views/Project/index.vue'),
    meta: { title: '项目管理', requiresAuth: true }
  },
  {
    path: '/project/list',
    name: 'ProjectList',
    component: () => import('@/views/Project/List.vue'),
    meta: { title: '项目列表', requiresAuth: true }
  },
  {
    path: '/project/create',
    name: 'ProjectCreate',
    component: () => import('@/views/Project/Create.vue'),
    meta: { title: '创建项目', requiresAuth: true }
  },
  {
    path: '/project/detail/:id',
    name: 'ProjectDetail',
    component: () => import('@/views/Project/Detail.vue'),
    meta: { title: '项目详情', requiresAuth: true }
  },
  // 兼容旧路由
  {
    path: '/projects',
    redirect: '/project/list'
  },
  {
    path: '/projects/:id',
    redirect: (to) => `/project/detail/${to.params.id}`
  },
  {
    path: '/audit',
    name: 'AuditIndex',
    component: () => import('@/views/Audit/index.vue'),
    meta: { title: '审计管理', requiresAuth: true }
  },
  {
    path: '/audit/list',
    name: 'AuditList',
    component: () => import('@/views/Audit/List.vue'),
    meta: { title: '审计任务列表', requiresAuth: true }
  },
  {
    path: '/audit/create',
    name: 'AuditCreate',
    component: () => import('@/views/Audit/Create.vue'),
    meta: { title: '创建审计任务', requiresAuth: true }
  },
  {
    path: '/audit/detail/:id',
    name: 'AuditDetail',
    component: () => import('@/views/Audit/Detail.vue'),
    meta: { title: '审计任务详情', requiresAuth: true }
  },
      {
        path: '/report',
        name: 'ReportIndex',
        component: () => import('@/views/Report/index.vue'),
        meta: { title: '报告管理' }
      },
      {
        path: '/report/list',
        name: 'ReportList',
        component: () => import('@/views/Report/List.vue'),
        meta: { title: '报告列表' }
      },
      {
        path: '/report/generate',
        name: 'ReportGenerate',
        component: () => import('@/views/Report/Generate.vue'),
        meta: { title: '生成报告' }
      },
      // 兼容旧路由
      {
        path: '/reports',
        redirect: '/report/list'
      },
      {
        path: '/vulnerability',
        name: 'VulnerabilityIndex',
        component: () => import('@/views/Vulnerability/index.vue'),
        meta: { title: '漏洞管理' }
      },
      {
        path: '/vulnerability/list',
        name: 'VulnerabilityList',
        component: () => import('@/views/Vulnerability/List.vue'),
        meta: { title: '漏洞列表' }
      },
      {
        path: '/vulnerability/detail/:id',
        name: 'VulnerabilityDetail',
        component: () => import('@/views/Vulnerability/Detail.vue'),
        meta: { title: '漏洞详情' }
      },
      {
        path: '/webhook',
        name: 'WebhookIndex',
        component: () => import('@/views/Webhook/index.vue'),
        meta: { title: 'Webhook管理' }
      },
      {
        path: '/webhook/list',
        name: 'WebhookList',
        component: () => import('@/views/Webhook/List.vue'),
        meta: { title: 'Webhook列表' }
      },
      {
        path: '/webhook/create',
        name: 'WebhookCreate',
        component: () => import('@/views/Webhook/Create.vue'),
        meta: { title: '创建Webhook' }
      },
      {
        path: '/webhook/detail/:id',
        name: 'WebhookDetail',
        component: () => import('@/views/Webhook/Detail.vue'),
        meta: { title: 'Webhook详情', requiresAuth: true }
      },

      // 系统管理路由
      {
        path: '/system/users',
        name: 'UserManagement',
        component: () => import('@/views/System/UserManagement.vue'),
        meta: { title: '用户管理', requiresAuth: true }
      },
      {
        path: '/system/settings',
        name: 'SystemSettings',
        component: () => import('@/views/System/Settings.vue'),
        meta: { title: '系统设置', requiresAuth: true }
      },

      // 个人资料
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: { title: '个人资料', requiresAuth: true }
      },

      // 搜索页面
      {
        path: '/search',
        name: 'Search',
        component: () => import('@/views/Search.vue'),
        meta: { title: '搜索结果', requiresAuth: true }
      },

      // 404页面
      {
        path: '/404',
        name: 'NotFound',
        component: () => import('@/views/404.vue'),
        meta: { title: '页面不存在', requiresAuth: false }
      },

      // 捕获所有未匹配的路由
      {
        path: '/:pathMatch(.*)*',
        redirect: '/404'
      }
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: { title: '系统设置' }
      },
      {
        path: '/user/profile',
        name: 'Profile',
        component: () => import('@/views/User/Profile.vue'),
        meta: { title: '个人资料' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const token = getToken()

  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - VulnAuditBox`
  }

  // 检查是否需要认证
  if (to.meta?.requiresAuth !== false) {
    if (!token) {
      next('/login')
      return
    }

    // 如果有token但用户信息为空，尝试获取用户信息
    if (!userStore.user && token) {
      try {
        await userStore.fetchUserInfo()
      } catch (error) {
        // 获取用户信息失败，清除token并跳转到登录页
        userStore.logout()
        next('/login')
        return
      }
    }
  }

  // 如果已登录用户访问登录/注册页，重定向到首页
  if (token && (to.path === '/login' || to.path === '/register')) {
    next('/dashboard')
    return
  }

  next()
})

export default router
