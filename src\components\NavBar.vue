<template>
  <a-menu
    v-model:selectedKeys="selectedKeys"
    v-model:openKeys="openKeys"
    mode="inline"
    theme="light"
    :inline-collapsed="collapsed"
    @click="handleMenuClick"
  >
    <!-- 仪表板 -->
    <a-menu-item key="/dashboard" v-if="hasPermission('dashboard:view')">
      <template #icon>
        <DashboardOutlined />
      </template>
      <span>仪表板</span>
    </a-menu-item>
    
    <!-- 项目管理 -->
    <a-sub-menu key="projects" v-if="hasPermission('project:view')">
      <template #icon>
        <ProjectOutlined />
      </template>
      <template #title>项目管理</template>
      <a-menu-item key="/project/list" v-if="hasPermission('project:list')">
        项目列表
      </a-menu-item>
      <a-menu-item key="/project/create" v-if="hasPermission('project:create')">
        创建项目
      </a-menu-item>
    </a-sub-menu>
    
    <!-- 审计管理 -->
    <a-sub-menu key="audits" v-if="hasPermission('audit:view')">
      <template #icon>
        <BugOutlined />
      </template>
      <template #title>审计管理</template>
      <a-menu-item key="/audit/list" v-if="hasPermission('audit:list')">
        审计列表
      </a-menu-item>
      <a-menu-item key="/audit/create" v-if="hasPermission('audit:create')">
        创建审计
      </a-menu-item>
    </a-sub-menu>
    
    <!-- 漏洞管理 -->
    <a-sub-menu key="vulnerabilities" v-if="hasPermission('vulnerability:view')">
      <template #icon>
        <ExclamationCircleOutlined />
      </template>
      <template #title>漏洞管理</template>
      <a-menu-item key="/vulnerability/list" v-if="hasPermission('vulnerability:list')">
        漏洞列表
      </a-menu-item>
    </a-sub-menu>
    
    <!-- 报告管理 -->
    <a-sub-menu key="reports" v-if="hasPermission('report:view')">
      <template #icon>
        <FileTextOutlined />
      </template>
      <template #title>报告管理</template>
      <a-menu-item key="/report/list" v-if="hasPermission('report:list')">
        报告列表
      </a-menu-item>
      <a-menu-item key="/report/generate" v-if="hasPermission('report:create')">
        生成报告
      </a-menu-item>
    </a-sub-menu>
    
    <!-- Webhook管理 -->
    <a-sub-menu key="webhooks" v-if="hasPermission('webhook:view')">
      <template #icon>
        <ApiOutlined />
      </template>
      <template #title>Webhook管理</template>
      <a-menu-item key="/webhook/list" v-if="hasPermission('webhook:list')">
        Webhook列表
      </a-menu-item>
      <a-menu-item key="/webhook/create" v-if="hasPermission('webhook:create')">
        创建Webhook
      </a-menu-item>
    </a-sub-menu>
    
    <!-- 系统管理 -->
    <a-sub-menu key="system" v-if="hasPermission('system:view')">
      <template #icon>
        <SettingOutlined />
      </template>
      <template #title>系统管理</template>
      <a-menu-item key="/system/users" v-if="hasPermission('user:manage')">
        用户管理
      </a-menu-item>
      <a-menu-item key="/system/roles" v-if="hasPermission('role:manage')">
        角色管理
      </a-menu-item>
      <a-menu-item key="/system/settings" v-if="hasPermission('system:settings')">
        系统设置
      </a-menu-item>
    </a-sub-menu>
  </a-menu>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  DashboardOutlined,
  ProjectOutlined,
  BugOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  ApiOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

interface Props {
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// 权限检查 - 临时修复：为登录用户提供基本权限
const hasPermission = (permission: string) => {
  // 如果用户已登录，暂时给予所有基本权限
  if (userStore.isLoggedIn) {
    return true
  }
  return userStore.hasPermission(permission)
}

// 根据当前路由设置选中的菜单项
const updateSelectedKeys = () => {
  const path = route.path
  selectedKeys.value = [path]
  
  // 设置展开的子菜单
  if (path.startsWith('/project')) {
    openKeys.value = ['projects']
  } else if (path.startsWith('/audit')) {
    openKeys.value = ['audits']
  } else if (path.startsWith('/vulnerability')) {
    openKeys.value = ['vulnerabilities']
  } else if (path.startsWith('/report')) {
    openKeys.value = ['reports']
  } else if (path.startsWith('/webhook')) {
    openKeys.value = ['webhooks']
  } else if (path.startsWith('/system')) {
    openKeys.value = ['system']
  }
}

// 菜单点击处理
const handleMenuClick = ({ key }: { key: string }) => {
  if (key !== route.path) {
    router.push(key)
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    updateSelectedKeys()
  },
  { immediate: true }
)

// 监听折叠状态变化
watch(
  () => props.collapsed,
  (collapsed) => {
    if (collapsed) {
      openKeys.value = []
    } else {
      updateSelectedKeys()
    }
  }
)
</script>

<style scoped>
:deep(.ant-menu-inline-collapsed > .ant-menu-item),
:deep(.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title) {
  padding: 0 20px;
}

:deep(.ant-menu-item-selected) {
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
}

:deep(.ant-menu-submenu-selected > .ant-menu-submenu-title) {
  color: #1890ff;
}
</style>
