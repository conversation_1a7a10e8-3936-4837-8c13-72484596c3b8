<template>
  <BaseChart
    :option="chartOption"
    :data="data"
    :loading="loading"
    :error="error"
    :width="width"
    :height="height"
    @retry="$emit('retry')"
    @chart-ready="$emit('chartReady', $event)"
    @chart-click="$emit('chartClick', $event)"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'
import type { EChartsOption } from 'echarts'

interface DataPoint {
  name: string
  value: number
}

interface SeriesData {
  name: string
  data: DataPoint[]
  color?: string
  smooth?: boolean
  areaStyle?: boolean
}

interface Props {
  data: SeriesData[]
  loading?: boolean
  error?: string
  width?: string
  height?: string
  title?: string
  subtitle?: string
  xAxisName?: string
  yAxisName?: string
  showLegend?: boolean
  showGrid?: boolean
  showTooltip?: boolean
  smooth?: boolean
  areaStyle?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: '',
  width: '100%',
  height: '400px',
  showLegend: true,
  showGrid: true,
  showTooltip: true,
  smooth: false,
  areaStyle: false
})

const emit = defineEmits<{
  retry: []
  chartReady: [chart: any]
  chartClick: [params: any]
}>()

const chartOption = computed((): EChartsOption => {
  if (!props.data || props.data.length === 0) {
    return {}
  }

  // 提取x轴数据
  const xAxisData = props.data[0]?.data.map(item => item.name) || []

  // 构建系列数据
  const series = props.data.map((seriesItem, index) => ({
    name: seriesItem.name,
    type: 'line',
    data: seriesItem.data.map(item => item.value),
    smooth: seriesItem.smooth ?? props.smooth,
    itemStyle: {
      color: seriesItem.color || getDefaultColor(index)
    },
    lineStyle: {
      width: 2
    },
    symbol: 'circle',
    symbolSize: 6,
    ...(seriesItem.areaStyle || props.areaStyle ? {
      areaStyle: {
        opacity: 0.3
      }
    } : {})
  }))

  return {
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      },
      subtextStyle: {
        fontSize: 12
      }
    } : undefined,
    
    tooltip: props.showTooltip ? {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    } : undefined,
    
    legend: props.showLegend ? {
      data: props.data.map(item => item.name),
      top: props.title ? 40 : 10,
      type: 'scroll'
    } : undefined,
    
    grid: props.showGrid ? {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: props.title ? (props.showLegend ? 80 : 60) : (props.showLegend ? 40 : 20),
      containLabel: true
    } : undefined,
    
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      name: props.xAxisName,
      nameLocation: 'middle',
      nameGap: 25,
      axisLabel: {
        rotate: xAxisData.length > 10 ? 45 : 0
      }
    },
    
    yAxis: {
      type: 'value',
      name: props.yAxisName,
      nameLocation: 'middle',
      nameGap: 40
    },
    
    series,
    
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }
})

// 获取默认颜色
const getDefaultColor = (index: number): string => {
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', 
    '#722ed1', '#13c2c2', '#eb2f96', '#fa541c'
  ]
  return colors[index % colors.length]
}
</script>
