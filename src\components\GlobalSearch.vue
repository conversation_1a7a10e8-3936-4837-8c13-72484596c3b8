<template>
  <div class="global-search">
    <a-input-search
      v-model:value="searchKeyword"
      placeholder="搜索项目、审计、漏洞..."
      size="large"
      :loading="searching"
      @search="handleSearch"
      @pressEnter="handleSearch"
      @focus="showResults = true"
      style="width: 400px"
    >
      <template #prefix>
        <SearchOutlined />
      </template>
    </a-input-search>
    
    <!-- 搜索结果下拉面板 -->
    <div 
      v-if="showResults && (searchResults.length > 0 || recentSearches.length > 0)"
      class="search-results"
      v-click-outside="handleClickOutside"
    >
      <!-- 最近搜索 -->
      <div v-if="!searchKeyword && recentSearches.length > 0" class="search-section">
        <div class="search-section-title">
          <HistoryOutlined />
          最近搜索
          <a-button type="text" size="small" @click="clearRecentSearches">
            清空
          </a-button>
        </div>
        <div class="search-items">
          <div 
            v-for="item in recentSearches" 
            :key="item.id"
            class="search-item recent-item"
            @click="handleRecentClick(item)"
          >
            <div class="search-item-icon">
              <component :is="getSearchIcon(item.type)" />
            </div>
            <div class="search-item-content">
              <div class="search-item-title">{{ item.title }}</div>
              <div class="search-item-desc">{{ item.description }}</div>
            </div>
            <div class="search-item-time">
              {{ formatTime(item.searchTime) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 搜索结果 -->
      <div v-if="searchKeyword && searchResults.length > 0">
        <!-- 项目结果 -->
        <div v-if="searchResults.filter(r => r.type === 'project').length > 0" class="search-section">
          <div class="search-section-title">
            <ProjectOutlined />
            项目 ({{ searchResults.filter(r => r.type === 'project').length }})
          </div>
          <div class="search-items">
            <div 
              v-for="item in searchResults.filter(r => r.type === 'project')" 
              :key="item.id"
              class="search-item"
              @click="handleResultClick(item)"
            >
              <div class="search-item-icon">
                <ProjectOutlined />
              </div>
              <div class="search-item-content">
                <div class="search-item-title" v-html="highlightKeyword(item.title)"></div>
                <div class="search-item-desc" v-html="highlightKeyword(item.description)"></div>
              </div>
              <div class="search-item-meta">
                <a-tag size="small" color="blue">项目</a-tag>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 审计结果 -->
        <div v-if="searchResults.filter(r => r.type === 'audit').length > 0" class="search-section">
          <div class="search-section-title">
            <BugOutlined />
            审计 ({{ searchResults.filter(r => r.type === 'audit').length }})
          </div>
          <div class="search-items">
            <div 
              v-for="item in searchResults.filter(r => r.type === 'audit')" 
              :key="item.id"
              class="search-item"
              @click="handleResultClick(item)"
            >
              <div class="search-item-icon">
                <BugOutlined />
              </div>
              <div class="search-item-content">
                <div class="search-item-title" v-html="highlightKeyword(item.title)"></div>
                <div class="search-item-desc" v-html="highlightKeyword(item.description)"></div>
              </div>
              <div class="search-item-meta">
                <a-tag size="small" color="green">审计</a-tag>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 漏洞结果 -->
        <div v-if="searchResults.filter(r => r.type === 'vulnerability').length > 0" class="search-section">
          <div class="search-section-title">
            <ExclamationCircleOutlined />
            漏洞 ({{ searchResults.filter(r => r.type === 'vulnerability').length }})
          </div>
          <div class="search-items">
            <div 
              v-for="item in searchResults.filter(r => r.type === 'vulnerability')" 
              :key="item.id"
              class="search-item"
              @click="handleResultClick(item)"
            >
              <div class="search-item-icon">
                <ExclamationCircleOutlined />
              </div>
              <div class="search-item-content">
                <div class="search-item-title" v-html="highlightKeyword(item.title)"></div>
                <div class="search-item-desc" v-html="highlightKeyword(item.description)"></div>
              </div>
              <div class="search-item-meta">
                <a-tag size="small" :color="getSeverityColor(item.severity)">
                  {{ item.severity }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 报告结果 -->
        <div v-if="searchResults.filter(r => r.type === 'report').length > 0" class="search-section">
          <div class="search-section-title">
            <FileTextOutlined />
            报告 ({{ searchResults.filter(r => r.type === 'report').length }})
          </div>
          <div class="search-items">
            <div 
              v-for="item in searchResults.filter(r => r.type === 'report')" 
              :key="item.id"
              class="search-item"
              @click="handleResultClick(item)"
            >
              <div class="search-item-icon">
                <FileTextOutlined />
              </div>
              <div class="search-item-content">
                <div class="search-item-title" v-html="highlightKeyword(item.title)"></div>
                <div class="search-item-desc" v-html="highlightKeyword(item.description)"></div>
              </div>
              <div class="search-item-meta">
                <a-tag size="small" color="orange">报告</a-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无结果 -->
      <div v-if="searchKeyword && searchResults.length === 0 && !searching" class="no-results">
        <a-empty description="未找到相关结果" />
        <div class="search-tips">
          <p>搜索建议：</p>
          <ul>
            <li>检查关键词拼写</li>
            <li>尝试使用更通用的关键词</li>
            <li>减少关键词数量</li>
          </ul>
        </div>
      </div>
      
      <!-- 查看更多 -->
      <div v-if="searchKeyword && searchResults.length > 0" class="search-footer">
        <a-button type="link" @click="handleViewMore">
          查看全部结果 ({{ totalResults }})
          <RightOutlined />
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { searchApi } from '@/api/search'
import { formatDate } from '@/utils/date'
import {
  SearchOutlined,
  HistoryOutlined,
  ProjectOutlined,
  BugOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  RightOutlined
} from '@ant-design/icons-vue'

interface SearchResult {
  id: string
  type: 'project' | 'audit' | 'vulnerability' | 'report'
  title: string
  description: string
  url: string
  severity?: string
  searchTime?: string
}

const router = useRouter()

const searchKeyword = ref('')
const searching = ref(false)
const showResults = ref(false)
const searchResults = ref<SearchResult[]>([])
const recentSearches = ref<SearchResult[]>([])
const totalResults = ref(0)

let searchTimeout: NodeJS.Timeout | null = null

// 获取搜索图标
const getSearchIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    project: ProjectOutlined,
    audit: BugOutlined,
    vulnerability: ExclamationCircleOutlined,
    report: FileTextOutlined
  }
  return iconMap[type] || SearchOutlined
}

// 获取严重程度颜色
const getSeverityColor = (severity?: string) => {
  const colorMap: Record<string, string> = {
    critical: 'red',
    high: 'orange',
    medium: 'yellow',
    low: 'green'
  }
  return colorMap[severity || ''] || 'default'
}

// 高亮关键词
const highlightKeyword = (text: string) => {
  if (!searchKeyword.value || !text) return text
  
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 格式化时间
const formatTime = (time: string) => {
  const now = new Date()
  const searchTime = new Date(time)
  const diff = now.getTime() - searchTime.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return formatDate(time, 'MM-DD')
}

// 执行搜索
const performSearch = async (keyword: string) => {
  if (!keyword.trim()) {
    searchResults.value = []
    return
  }
  
  searching.value = true
  try {
    const response = await searchApi.globalSearch({
      keyword: keyword.trim(),
      limit: 20
    })
    
    searchResults.value = response.data.results
    totalResults.value = response.data.total
  } catch (error) {
    console.error('Search error:', error)
    searchResults.value = []
    totalResults.value = 0
  } finally {
    searching.value = false
  }
}

// 处理搜索
const handleSearch = (value?: string) => {
  const keyword = value || searchKeyword.value
  if (!keyword.trim()) return
  
  // 防抖处理
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  searchTimeout = setTimeout(() => {
    performSearch(keyword)
  }, 300)
}

// 处理结果点击
const handleResultClick = (item: SearchResult) => {
  // 添加到最近搜索
  addToRecentSearches(item)
  
  // 跳转到对应页面
  router.push(item.url)
  
  // 关闭搜索结果
  showResults.value = false
  searchKeyword.value = ''
}

// 处理最近搜索点击
const handleRecentClick = (item: SearchResult) => {
  searchKeyword.value = item.title
  handleSearch(item.title)
}

// 查看更多结果
const handleViewMore = () => {
  router.push({
    path: '/search',
    query: { q: searchKeyword.value }
  })
  showResults.value = false
}

// 点击外部关闭
const handleClickOutside = () => {
  showResults.value = false
}

// 添加到最近搜索
const addToRecentSearches = (item: SearchResult) => {
  const newItem = {
    ...item,
    searchTime: new Date().toISOString()
  }
  
  // 移除重复项
  recentSearches.value = recentSearches.value.filter(r => r.id !== item.id)
  
  // 添加到开头
  recentSearches.value.unshift(newItem)
  
  // 限制数量
  if (recentSearches.value.length > 10) {
    recentSearches.value = recentSearches.value.slice(0, 10)
  }
  
  // 保存到localStorage
  saveRecentSearches()
}

// 清空最近搜索
const clearRecentSearches = () => {
  recentSearches.value = []
  localStorage.removeItem('vulnauditbox_recent_searches')
}

// 保存最近搜索
const saveRecentSearches = () => {
  try {
    localStorage.setItem('vulnauditbox_recent_searches', JSON.stringify(recentSearches.value))
  } catch (error) {
    console.error('Failed to save recent searches:', error)
  }
}

// 加载最近搜索
const loadRecentSearches = () => {
  try {
    const stored = localStorage.getItem('vulnauditbox_recent_searches')
    if (stored) {
      recentSearches.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('Failed to load recent searches:', error)
    recentSearches.value = []
  }
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    const searchInput = document.querySelector('.global-search input') as HTMLInputElement
    if (searchInput) {
      searchInput.focus()
      showResults.value = true
    }
  }
  
  // ESC 关闭搜索结果
  if (event.key === 'Escape') {
    showResults.value = false
  }
}

onMounted(() => {
  loadRecentSearches()
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.global-search {
  position: relative;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  max-height: 500px;
  overflow-y: auto;
}

.search-section {
  padding: 12px 0;
}

.search-section:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.search-section-title {
  display: flex;
  align-items: center;
  padding: 0 16px 8px 16px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  gap: 6px;
}

.search-section-title .ant-btn {
  margin-left: auto;
}

.search-items {
  padding: 0;
}

.search-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-item:hover {
  background-color: #f5f5f5;
}

.search-item-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 6px;
  margin-right: 12px;
  font-size: 14px;
  color: #666;
}

.search-item-content {
  flex: 1;
  min-width: 0;
}

.search-item-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-item-desc {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-item-meta {
  margin-left: 8px;
}

.search-item-time {
  font-size: 11px;
  color: #999;
  margin-left: 8px;
}

.recent-item .search-item-icon {
  background: #e6f7ff;
  color: #1890ff;
}

.no-results {
  padding: 40px 20px;
  text-align: center;
}

.search-tips {
  margin-top: 16px;
  text-align: left;
  max-width: 200px;
  margin-left: auto;
  margin-right: auto;
}

.search-tips p {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.search-tips ul {
  font-size: 11px;
  color: #999;
  padding-left: 16px;
}

.search-tips li {
  margin-bottom: 2px;
}

.search-footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

:deep(mark) {
  background: #fff2b8;
  padding: 0 2px;
  border-radius: 2px;
}

:deep(.ant-input-search) {
  border-radius: 8px;
}

:deep(.ant-input-search .ant-input) {
  border-radius: 8px;
}

/* 暗黑模式适配 */
[data-theme='dark'] .search-results {
  background: #1f1f1f;
  border: 1px solid #303030;
}

[data-theme='dark'] .search-section:not(:last-child) {
  border-bottom-color: #303030;
}

[data-theme='dark'] .search-section-title {
  color: #a6a6a6;
}

[data-theme='dark'] .search-item:hover {
  background-color: #262626;
}

[data-theme='dark'] .search-item-icon {
  background: #262626;
  color: #a6a6a6;
}

[data-theme='dark'] .search-item-desc {
  color: #a6a6a6;
}

[data-theme='dark'] .search-item-time {
  color: #737373;
}

[data-theme='dark'] .recent-item .search-item-icon {
  background: #111b26;
  color: #177ddc;
}

[data-theme='dark'] .search-footer {
  border-top-color: #303030;
}

[data-theme='dark'] :deep(mark) {
  background: #614700;
  color: #fff;
}
</style>
