<template>
  <div class="register-container">
    <!-- 主题切换按钮 -->
    <div class="theme-toggle-wrapper">
      <ThemeToggle mode="simple" />
    </div>

    <div class="register-box">
      <div class="register-header">
        <img src="/logo.svg" alt="VulnAuditBox" class="logo" />
        <h1>注册账号</h1>
        <p>创建您的VulnAuditBox账号</p>
      </div>
      
      <a-form
        ref="formRef"
        :model="registerForm"
        :rules="rules"
        @finish="handleRegister"
        class="register-form"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username">
          <a-input
            v-model:value="registerForm.username"
            size="large"
            placeholder="请输入用户名（3-50字符）"
            :prefix="h(UserOutlined)"
          />
        </a-form-item>
        
        <a-form-item label="邮箱" name="email">
          <a-input
            v-model:value="registerForm.email"
            size="large"
            placeholder="请输入邮箱地址"
            :prefix="h(MailOutlined)"
          />
        </a-form-item>
        
        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="registerForm.password"
            size="large"
            placeholder="请输入密码（至少8位，包含大小写字母和数字）"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>
        
        <a-form-item label="确认密码" name="confirm_password">
          <a-input-password
            v-model:value="registerForm.confirm_password"
            size="large"
            placeholder="请再次输入密码"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="userStore.loading"
          >
            注册
          </a-button>
        </a-form-item>
        
        <div class="register-footer">
          <span>已有账号？</span>
          <router-link to="/login">立即登录</router-link>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, h } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons-vue'
import { isValidEmail } from '@/utils/common'
import ThemeToggle from '@/components/ThemeToggle.vue'
import type { RegisterForm } from '@/types/user'
import type { FormInstance } from 'ant-design-vue'

const router = useRouter()
const userStore = useUserStore()
const formRef = ref<FormInstance>()

const registerForm = reactive<RegisterForm>({
  username: '',
  email: '',
  password: '',
  confirm_password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度为3-50个字符', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      message: '用户名只能包含字母、数字、下划线、连字符', 
      trigger: 'blur' 
    }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { 
      validator: (_: any, value: string) => {
        if (!value) return Promise.resolve()
        if (!isValidEmail(value)) {
          return Promise.reject(new Error('请输入有效的邮箱地址'))
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, max: 128, message: '密码长度为8-128位', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/, 
      message: '密码必须包含大小写字母和数字', 
      trigger: 'blur' 
    }
  ],
  confirm_password: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { 
      validator: (_: any, value: string) => {
        if (!value) return Promise.resolve()
        if (value !== registerForm.password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ]
}

const handleRegister = async () => {
  try {
    await userStore.register(registerForm)
    router.push('/login')
  } catch (error) {
    // 错误已在store中处理
    console.error('Register error:', error)
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.theme-toggle-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.register-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  height: 64px;
  margin-bottom: 16px;
}

.register-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.register-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.register-form {
  margin-top: 32px;
}

.register-footer {
  text-align: center;
  margin-top: 16px;
  color: #8c8c8c;
}

.register-footer a {
  color: #1890ff;
  text-decoration: none;
  margin-left: 4px;
}

.register-footer a:hover {
  text-decoration: underline;
}
</style>
