<template>
  <div class="forgot-password-page">
    <div class="forgot-password-container">
      <div class="forgot-password-card">
        <div class="forgot-password-header">
          <img src="/logo.svg" alt="VulnAuditBox" class="logo" />
          <h1>找回密码</h1>
          <p>请输入您的邮箱地址，我们将发送重置密码的链接</p>
        </div>
        
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          layout="vertical"
          @finish="handleSubmit"
          class="forgot-password-form"
        >
          <a-form-item name="email">
            <a-input
              v-model:value="form.email"
              size="large"
              placeholder="请输入邮箱地址"
              :prefix="h(MailOutlined)"
            />
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              :loading="loading"
              block
            >
              发送重置链接
            </a-button>
          </a-form-item>
          
          <div class="forgot-password-footer">
            <a @click="$router.push('/login')">返回登录</a>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { MailOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = reactive({
  email: ''
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

const handleSubmit = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    message.success('重置密码链接已发送到您的邮箱，请查收')
    
    // 3秒后跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 3000)
    
  } catch (error) {
    message.error('发送失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.forgot-password-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.forgot-password-container {
  width: 100%;
  max-width: 400px;
}

.forgot-password-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.forgot-password-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.forgot-password-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.forgot-password-header p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.forgot-password-form {
  margin-top: 32px;
}

.forgot-password-footer {
  text-align: center;
  margin-top: 24px;
}

.forgot-password-footer a {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.forgot-password-footer a:hover {
  text-decoration: underline;
}

/* 暗黑模式适配 */
[data-theme='dark'] .forgot-password-card {
  background: #1f1f1f;
  border: 1px solid #303030;
}

[data-theme='dark'] .forgot-password-header h1 {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .forgot-password-header p {
  color: rgba(255, 255, 255, 0.65);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .forgot-password-card {
    padding: 24px;
  }
  
  .forgot-password-header h1 {
    font-size: 24px;
  }
  
  .logo {
    width: 48px;
    height: 48px;
  }
}
</style>
