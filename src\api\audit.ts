import api from './index'
import type { 
  Audit,
  AuditDetailResponse,
  CreateAuditForm,
  AuditQueryParams,
  AuditProgress,
  AuditStatistics,
  AuditCancelRequest
} from '@/types/audit'

export const auditApi = {
  // 获取审计列表
  getAudits: (params?: AuditQueryParams) =>
    api.get<Audit[]>('/audits/', { params }),

  // 获取审计详情
  getAudit: (id: number) =>
    api.get<AuditDetailResponse>(`/audits/${id}`),

  // 创建审计
  createAudit: (data: CreateAuditForm) =>
    api.post<Audit>('/audits/', data),

  // 获取审计进度
  getAuditProgress: (id: number) =>
    api.get<AuditProgress>(`/audits/${id}/progress`),

  // 取消审计
  cancelAudit: (id: number, data?: AuditCancelRequest) =>
    api.post<{ message: string }>(`/audits/${id}/cancel`, data || {}),

  // 获取审计统计
  getAuditStatistics: () =>
    api.get<AuditStatistics>('/audits/statistics'),

  // 重新运行审计
  rerunAudit: (id: number) =>
    api.post<Audit>(`/audits/${id}/rerun`),
}
