<template>
  <BaseChart
    :option="chartOption"
    :data="data"
    :loading="loading"
    :error="error"
    :width="width"
    :height="height"
    @retry="$emit('retry')"
    @chart-ready="$emit('chartReady', $event)"
    @chart-click="$emit('chartClick', $event)"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'
import type { EChartsOption } from 'echarts'

interface DataPoint {
  name: string
  value: number
}

interface SeriesData {
  name: string
  data: DataPoint[]
  color?: string
  stack?: string
}

interface Props {
  data: SeriesData[]
  loading?: boolean
  error?: string
  width?: string
  height?: string
  title?: string
  subtitle?: string
  xAxisName?: string
  yAxisName?: string
  showLegend?: boolean
  showGrid?: boolean
  showTooltip?: boolean
  horizontal?: boolean
  stack?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: '',
  width: '100%',
  height: '400px',
  showLegend: true,
  showGrid: true,
  showTooltip: true,
  horizontal: false,
  stack: false
})

const emit = defineEmits<{
  retry: []
  chartReady: [chart: any]
  chartClick: [params: any]
}>()

const chartOption = computed((): EChartsOption => {
  if (!props.data || props.data.length === 0) {
    return {}
  }

  // 提取轴数据
  const axisData = props.data[0]?.data.map(item => item.name) || []

  // 构建系列数据
  const series = props.data.map((seriesItem, index) => ({
    name: seriesItem.name,
    type: 'bar',
    data: seriesItem.data.map(item => item.value),
    itemStyle: {
      color: seriesItem.color || getDefaultColor(index),
      borderRadius: props.horizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]
    },
    stack: props.stack ? (seriesItem.stack || 'default') : undefined,
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      }
    }
  }))

  const baseConfig = {
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      },
      subtextStyle: {
        fontSize: 12
      }
    } : undefined,
    
    tooltip: props.showTooltip ? {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    } : undefined,
    
    legend: props.showLegend ? {
      data: props.data.map(item => item.name),
      top: props.title ? 40 : 10,
      type: 'scroll'
    } : undefined,
    
    grid: props.showGrid ? {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: props.title ? (props.showLegend ? 80 : 60) : (props.showLegend ? 40 : 20),
      containLabel: true
    } : undefined,
    
    series,
    
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
    animationDelay: (idx: number) => idx * 100
  }

  // 根据方向设置轴配置
  if (props.horizontal) {
    return {
      ...baseConfig,
      xAxis: {
        type: 'value',
        name: props.xAxisName,
        nameLocation: 'middle',
        nameGap: 25
      },
      yAxis: {
        type: 'category',
        data: axisData,
        name: props.yAxisName,
        nameLocation: 'middle',
        nameGap: 40,
        axisLabel: {
          interval: 0,
          rotate: axisData.some(name => name.length > 6) ? 0 : 0
        }
      }
    }
  } else {
    return {
      ...baseConfig,
      xAxis: {
        type: 'category',
        data: axisData,
        name: props.xAxisName,
        nameLocation: 'middle',
        nameGap: 25,
        axisLabel: {
          interval: 0,
          rotate: axisData.some(name => name.length > 4) ? 45 : 0
        }
      },
      yAxis: {
        type: 'value',
        name: props.yAxisName,
        nameLocation: 'middle',
        nameGap: 40
      }
    }
  }
})

// 获取默认颜色
const getDefaultColor = (index: number): string => {
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', 
    '#722ed1', '#13c2c2', '#eb2f96', '#fa541c'
  ]
  return colors[index % colors.length]
}
</script>
