<template>
  <div id="app">
    <a-config-provider :theme="themeConfig">
      <router-view v-if="!useMainLayout" />
      <MainLayout v-else />
    </a-config-provider>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useWebSocketStore } from '@/stores/websocket'
import { useThemeStore } from '@/stores/themeStore'
import MainLayout from '@/components/Layout/MainLayout.vue'

const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()
const webSocketStore = useWebSocketStore()
const themeStore = useThemeStore()

// 不使用主布局的路由
const noLayoutRoutes = ['/login', '/register', '/forgot-password', '/404', '/403']

// 判断是否使用主布局
const useMainLayout = computed(() => {
  return !noLayoutRoutes.includes(route.path) && userStore.isLoggedIn
})

// 使用主题Store的配置
const themeConfig = computed(() => themeStore.antdThemeConfig)

let themeCleanup: (() => void) | undefined

onMounted(async () => {
  // 初始化主题
  themeCleanup = themeStore.initTheme()

  // 初始化用户认证状态
  await userStore.initializeAuth()

  // 如果用户已登录，连接WebSocket
  if (userStore.isLoggedIn) {
    webSocketStore.connect()
  }
})

onUnmounted(() => {
  // 清理主题监听器
  if (themeCleanup) {
    themeCleanup()
  }
})
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}
</style>
