import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  User,
  LoginForm,
  RegisterForm,
  ChangePasswordForm,
  UserUpdate
} from '@/types/user'
import { userApi } from '@/api/user'
import { setToken, removeToken, getToken } from '@/utils/auth'
import { message } from 'ant-design-vue'

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const loading = ref(false)

  const isLoggedIn = computed(() => !!user.value && !!getToken())
  const userRole = computed(() => user.value?.role || '')
  const userPermissions = computed(() => user.value?.permissions || [])

  // 权限检查
  const hasPermission = (permission: string) => {
    if (!user.value) return false

    // 超级管理员拥有所有权限
    if (user.value.role === 'admin') return true

    // 检查具体权限
    return user.value.permissions?.includes(permission) || false
  }

  // 角色检查
  const hasRole = (role: string) => {
    return user.value?.role === role
  }

  // 登录
  const login = async (loginForm: LoginForm) => {
    loading.value = true
    try {
      const response = await userApi.login(loginForm)
      const { access_token, refresh_token } = response.data

      // 存储token
      setToken(access_token)
      localStorage.setItem('refresh_token', refresh_token)

      // 获取用户信息
      await fetchUserInfo()

      message.success('登录成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterForm) => {
    loading.value = true
    try {
      const response = await userApi.register(registerForm)
      message.success('注册成功，请登录')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await userApi.getUserInfo()
      user.value = response.data
      return response
    } catch (error) {
      throw error
    }
  }

  // 更新用户信息
  const updateUserInfo = async (userInfo: UserUpdate) => {
    loading.value = true
    try {
      const response = await userApi.updateUserInfo(userInfo)
      user.value = response.data
      message.success('更新成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '更新失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordForm: ChangePasswordForm) => {
    loading.value = true
    try {
      const response = await userApi.changePassword(passwordForm)
      message.success('密码修改成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '密码修改失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 初始化认证状态
  const initializeAuth = async () => {
    const token = getToken()
    if (token && !user.value) {
      try {
        await fetchUserInfo()
      } catch (error) {
        // 如果获取用户信息失败，清除token
        logout()
      }
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    removeToken()
    localStorage.removeItem('refresh_token')
    message.success('已退出登录')
  }

  return {
    user,
    loading,
    isLoggedIn,
    userRole,
    userPermissions,
    hasPermission,
    hasRole,
    login,
    register,
    fetchUserInfo,
    updateUserInfo,
    changePassword,
    initializeAuth,
    logout
  }
})
