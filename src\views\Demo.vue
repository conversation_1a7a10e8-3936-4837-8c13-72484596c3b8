<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>VulnAuditBox 系统扩展演示</h1>
      <p>展示主题管理、全局搜索和数据可视化功能</p>
    </div>
    
    <a-row :gutter="[24, 24]">
      <!-- 主题切换演示 -->
      <a-col :span="24">
        <a-card title="主题管理系统">
          <div class="theme-demo">
            <h3>主题切换</h3>
            <a-space size="large">
              <div>
                <p>简单切换：</p>
                <ThemeToggle mode="simple" />
              </div>
              <div>
                <p>下拉菜单：</p>
                <ThemeToggle mode="dropdown" />
              </div>
            </a-space>
            
            <a-divider />
            
            <h3>完整主题设置面板</h3>
            <ThemeToggle mode="panel" />
          </div>
        </a-card>
      </a-col>
      
      <!-- 全局搜索演示 -->
      <a-col :span="24">
        <a-card title="全局搜索系统">
          <div class="search-demo">
            <h3>全局搜索组件</h3>
            <div style="display: flex; justify-content: center; margin: 20px 0;">
              <GlobalSearch />
            </div>
            <p>功能特性：</p>
            <ul>
              <li>支持项目、审计、漏洞、报告的全局搜索</li>
              <li>实时搜索建议和结果预览</li>
              <li>最近搜索历史记录</li>
              <li>按类型分组显示结果</li>
              <li>关键词高亮显示</li>
              <li>快捷键支持（Ctrl/Cmd + K）</li>
            </ul>
          </div>
        </a-card>
      </a-col>
      
      <!-- 数据可视化演示 -->
      <a-col :span="24">
        <a-card title="数据可视化组件">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :lg="12">
              <h3>折线图组件</h3>
              <LineChart
                :data="lineChartData"
                title="审计趋势"
                x-axis-name="日期"
                y-axis-name="审计次数"
                :smooth="true"
                :area-style="true"
                height="300px"
              />
            </a-col>
            
            <a-col :xs="24" :lg="12">
              <h3>饼图组件</h3>
              <PieChart
                :data="pieChartData"
                title="漏洞严重程度分布"
                :show-legend="true"
                :radius="['40%', '70%']"
                height="300px"
              />
            </a-col>
            
            <a-col :span="24">
              <h3>柱状图组件</h3>
              <BarChart
                :data="barChartData"
                title="月度活动统计"
                x-axis-name="月份"
                y-axis-name="数量"
                height="300px"
              />
            </a-col>
          </a-row>
        </a-card>
      </a-col>
      
      <!-- 主题信息展示 -->
      <a-col :span="24">
        <a-card title="当前主题信息">
          <a-descriptions :column="2">
            <a-descriptions-item label="当前主题">
              {{ themeStore.currentTheme }}
            </a-descriptions-item>
            <a-descriptions-item label="是否暗黑模式">
              {{ themeStore.isDarkMode ? '是' : '否' }}
            </a-descriptions-item>
            <a-descriptions-item label="主色调">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div 
                  :style="{ 
                    width: '20px', 
                    height: '20px', 
                    backgroundColor: themeStore.themeConfig.primaryColor,
                    borderRadius: '4px',
                    border: '1px solid #d9d9d9'
                  }"
                ></div>
                {{ themeStore.themeConfig.primaryColor }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="圆角大小">
              {{ themeStore.themeConfig.borderRadius }}px
            </a-descriptions-item>
            <a-descriptions-item label="紧凑模式">
              {{ themeStore.themeConfig.compactMode ? '开启' : '关闭' }}
            </a-descriptions-item>
            <a-descriptions-item label="色弱模式">
              {{ themeStore.themeConfig.colorWeakMode ? '开启' : '关闭' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useThemeStore } from '@/stores/themeStore'
import ThemeToggle from '@/components/ThemeToggle.vue'
import GlobalSearch from '@/components/GlobalSearch.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import PieChart from '@/components/Charts/PieChart.vue'
import BarChart from '@/components/Charts/BarChart.vue'

const themeStore = useThemeStore()

// 示例图表数据
const lineChartData = ref([
  {
    name: '审计次数',
    data: [
      { name: '01/15', value: 12 },
      { name: '01/16', value: 19 },
      { name: '01/17', value: 15 },
      { name: '01/18', value: 25 },
      { name: '01/19', value: 22 },
      { name: '01/20', value: 30 },
      { name: '01/21', value: 28 }
    ],
    color: '#1890ff',
    smooth: true,
    areaStyle: true
  }
])

const pieChartData = ref([
  { name: '严重', value: 5, color: '#f5222d' },
  { name: '高危', value: 12, color: '#fa541c' },
  { name: '中危', value: 18, color: '#faad14' },
  { name: '低危', value: 25, color: '#52c41a' }
])

const barChartData = ref([
  {
    name: '项目',
    data: [
      { name: '1月', value: 15 },
      { name: '2月', value: 18 },
      { name: '3月', value: 22 },
      { name: '4月', value: 25 },
      { name: '5月', value: 28 },
      { name: '6月', value: 32 }
    ],
    color: '#1890ff'
  },
  {
    name: '审计',
    data: [
      { name: '1月', value: 25 },
      { name: '2月', value: 30 },
      { name: '3月', value: 35 },
      { name: '4月', value: 40 },
      { name: '5月', value: 45 },
      { name: '6月', value: 50 }
    ],
    color: '#52c41a'
  },
  {
    name: '漏洞',
    data: [
      { name: '1月', value: 45 },
      { name: '2月', value: 52 },
      { name: '3月', value: 48 },
      { name: '4月', value: 55 },
      { name: '5月', value: 60 },
      { name: '6月', value: 58 }
    ],
    color: '#faad14'
  }
])
</script>

<style scoped>
.demo-page {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h1 {
  font-size: 32px;
  margin-bottom: 8px;
  color: var(--ant-color-text);
}

.demo-header p {
  font-size: 16px;
  color: var(--ant-color-text-secondary);
}

.theme-demo h3,
.search-demo h3 {
  margin-bottom: 16px;
  color: var(--ant-color-text);
}

.search-demo ul {
  margin-top: 16px;
}

.search-demo li {
  margin-bottom: 8px;
  color: var(--ant-color-text-secondary);
}

/* 暗黑模式适配 */
[data-theme='dark'] .demo-page {
  background: #141414;
}

[data-theme='dark'] .demo-header h1 {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .demo-header p {
  color: rgba(255, 255, 255, 0.65);
}

[data-theme='dark'] .theme-demo h3,
[data-theme='dark'] .search-demo h3 {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .search-demo li {
  color: rgba(255, 255, 255, 0.65);
}
</style>
