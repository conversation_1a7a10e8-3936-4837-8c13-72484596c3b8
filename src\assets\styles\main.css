/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.mb-16 {
  margin-bottom: 16px;
}

.mt-16 {
  margin-top: 16px;
}

.p-16 {
  padding: 16px;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 自定义组件样式 */
.ant-layout-sider-collapsed .ant-menu-item-icon {
  font-size: 16px;
}

.ant-layout-sider-collapsed .ant-menu-submenu-title {
  padding: 0 calc(50% - 8px) !important;
}

/* 暗黑模式支持 */
[data-theme='dark'] {
  background-color: #141414;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .ant-layout {
  background-color: #141414;
}

[data-theme='dark'] .ant-layout-content {
  background-color: #1f1f1f;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .ant-layout-sider {
    display: none !important;
  }
  
  .ant-layout-header {
    display: none !important;
  }
}

/* 主题相关样式 */
:root {
  --primary-color: #1890ff;
  --border-radius: 6px;
}

/* 暗黑模式样式 */
[data-theme='dark'] {
  --ant-color-bg-base: #141414;
  --ant-color-bg-container: #1f1f1f;
  --ant-color-bg-elevated: #262626;
  --ant-color-text: rgba(255, 255, 255, 0.85);
  --ant-color-text-secondary: rgba(255, 255, 255, 0.65);
  --ant-color-border: #303030;
}

[data-theme='dark'] body {
  background-color: #141414;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .ant-layout {
  background: #141414;
}

[data-theme='dark'] .ant-card {
  background: #1f1f1f;
  border-color: #303030;
}

[data-theme='dark'] .ant-table {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-table-thead > tr > th {
  background: #262626;
  border-color: #303030;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .ant-table-tbody > tr > td {
  border-color: #303030;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .ant-table-tbody > tr:hover > td {
  background: #262626;
}

[data-theme='dark'] .ant-input,
[data-theme='dark'] .ant-input-password,
[data-theme='dark'] .ant-select-selector,
[data-theme='dark'] .ant-textarea {
  background: #1f1f1f;
  border-color: #303030;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .ant-btn {
  border-color: #303030;
}

[data-theme='dark'] .ant-btn:not(.ant-btn-primary) {
  background: #1f1f1f;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .ant-modal-content {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-modal-header {
  background: #1f1f1f;
  border-color: #303030;
}

[data-theme='dark'] .ant-modal-title {
  color: rgba(255, 255, 255, 0.85);
}

/* 色弱模式样式 */
.color-weak {
  filter: invert(80%);
}

.color-weak .ant-btn-primary {
  background-color: #d48806;
  border-color: #d48806;
}

.color-weak .ant-tag {
  filter: grayscale(50%);
}

/* 图表容器样式 */
.chart-container {
  background: var(--ant-color-bg-container);
  border-radius: var(--border-radius);
  padding: 16px;
}

[data-theme='dark'] .chart-container {
  background: #1f1f1f;
}

/* 搜索高亮样式 */
mark {
  background: #fff2b8;
  padding: 0 2px;
  border-radius: 2px;
}

[data-theme='dark'] mark {
  background: #614700;
  color: #fff;
}

/* 主题切换动画 */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* 禁用过渡动画的元素 */
.ant-switch,
.ant-slider,
.ant-progress,
.ant-spin {
  transition: none !important;
}

.ant-switch * {
  transition: none !important;
}
