<template>
  <div class="profile-page">
    <div class="page-header">
      <h2>个人资料</h2>
    </div>
    
    <a-row :gutter="[16, 16]">
      <!-- 基本信息 -->
      <a-col :xs="24" :lg="16">
        <a-card title="基本信息" :loading="userStore.loading">
          <a-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            layout="vertical"
            @finish="handleUpdateProfile"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名" name="username">
                  <a-input v-model:value="profileForm.username" placeholder="请输入用户名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮箱" name="email">
                  <a-input v-model:value="profileForm.email" placeholder="请输入邮箱" />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="userStore.loading">
                更新资料
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 头像上传 -->
      <a-col :xs="24" :lg="8">
        <a-card title="头像设置">
          <div class="avatar-section">
            <a-avatar :src="userStore.user?.avatar" :size="120">
              {{ userStore.user?.username?.charAt(0).toUpperCase() }}
            </a-avatar>
            
            <a-upload
              :show-upload-list="false"
              :before-upload="handleBeforeUpload"
              :custom-request="handleUploadAvatar"
              accept="image/*"
            >
              <a-button style="margin-top: 16px">
                <template #icon>
                  <UploadOutlined />
                </template>
                上传头像
              </a-button>
            </a-upload>
            
            <p class="avatar-tip">
              支持 JPG、PNG 格式，文件大小不超过 2MB
            </p>
          </div>
        </a-card>
      </a-col>
      
      <!-- 修改密码 -->
      <a-col :span="24">
        <a-card title="修改密码">
          <a-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            layout="vertical"
            @finish="handleChangePassword"
            style="max-width: 400px"
          >
            <a-form-item label="当前密码" name="oldPassword">
              <a-input-password v-model:value="passwordForm.oldPassword" placeholder="请输入当前密码" />
            </a-form-item>
            
            <a-form-item label="新密码" name="newPassword">
              <a-input-password v-model:value="passwordForm.newPassword" placeholder="请输入新密码" />
            </a-form-item>
            
            <a-form-item label="确认新密码" name="confirmPassword">
              <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请再次输入新密码" />
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="passwordLoading">
                修改密码
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 通知设置 -->
      <a-col :span="24">
        <a-card title="通知设置">
          <a-form layout="vertical">
            <a-form-item label="邮件通知">
              <a-checkbox-group v-model:value="notificationSettings.email">
                <a-row>
                  <a-col :span="8">
                    <a-checkbox value="audit_completed">审计完成</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="audit_failed">审计失败</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="vulnerability_found">发现漏洞</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="report_generated">报告生成</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="webhook_failed">Webhook失败</a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="桌面通知">
              <a-checkbox-group v-model:value="notificationSettings.desktop">
                <a-row>
                  <a-col :span="8">
                    <a-checkbox value="audit_completed">审计完成</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="vulnerability_found">发现漏洞</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="report_generated">报告生成</a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" @click="handleSaveNotifications" :loading="notificationLoading">
                保存通知设置
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 账户信息 -->
      <a-col :span="24">
        <a-card title="账户信息">
          <a-descriptions :column="2" v-if="userStore.user">
            <a-descriptions-item label="用户ID">
              {{ userStore.user.id }}
            </a-descriptions-item>
            <a-descriptions-item label="角色">
              <a-tag :color="getRoleColor(userStore.user.role)">
                {{ getRoleText(userStore.user.role) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="注册时间">
              {{ formatDate(userStore.user.createdAt) }}
            </a-descriptions-item>
            <a-descriptions-item label="最后登录">
              {{ userStore.user.lastLoginAt ? formatDate(userStore.user.lastLoginAt) : '从未登录' }}
            </a-descriptions-item>
            <a-descriptions-item label="权限" :span="2">
              <a-space wrap>
                <a-tag v-for="permission in userStore.user.permissions" :key="permission" color="blue">
                  {{ getPermissionName(permission) }}
                </a-tag>
              </a-space>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useNotificationStore } from '@/stores/notification'
import { formatDate } from '@/utils/date'
import { message } from 'ant-design-vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import type { FormInstance, UploadProps } from 'ant-design-vue'

const userStore = useUserStore()
const notificationStore = useNotificationStore()

const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const passwordLoading = ref(false)
const notificationLoading = ref(false)

const profileForm = reactive({
  username: '',
  email: ''
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const notificationSettings = reactive({
  email: ['audit_completed', 'vulnerability_found'],
  desktop: ['audit_completed', 'vulnerability_found']
})

const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (_, value) => {
        if (value !== passwordForm.newPassword) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

const getRoleColor = (role: string) => {
  const colorMap: Record<string, string> = {
    admin: 'red',
    user: 'blue',
    auditor: 'green'
  }
  return colorMap[role] || 'default'
}

const getRoleText = (role: string) => {
  const textMap: Record<string, string> = {
    admin: '管理员',
    user: '普通用户',
    auditor: '审计员'
  }
  return textMap[role] || role
}

const getPermissionName = (permission: string) => {
  const permissionMap: Record<string, string> = {
    'dashboard:view': '仪表板查看',
    'project:view': '项目查看',
    'project:create': '项目创建',
    'project:edit': '项目编辑',
    'audit:view': '审计查看',
    'audit:create': '审计创建',
    'vulnerability:view': '漏洞查看',
    'report:view': '报告查看',
    'webhook:view': 'Webhook查看',
    'system:settings': '系统设置',
    'user:manage': '用户管理'
  }
  return permissionMap[permission] || permission
}

const handleUpdateProfile = async () => {
  try {
    await userStore.updateUserInfo(profileForm)
  } catch (error) {
    console.error('Update profile error:', error)
  }
}

const handleChangePassword = async () => {
  passwordLoading.value = true
  try {
    await userStore.changePassword(passwordForm.oldPassword, passwordForm.newPassword)
    
    // 重置表单
    passwordFormRef.value?.resetFields()
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  } catch (error) {
    console.error('Change password error:', error)
  } finally {
    passwordLoading.value = false
  }
}

const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    message.error('只能上传图片文件!')
    return false
  }
  
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!')
    return false
  }
  
  return true
}

const handleUploadAvatar: UploadProps['customRequest'] = async (options) => {
  try {
    // 模拟上传头像
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建预览URL
    const file = options.file as File
    const imageUrl = URL.createObjectURL(file)
    
    // 更新用户头像
    await userStore.updateUserInfo({ avatar: imageUrl })
    
    message.success('头像上传成功')
    options.onSuccess?.(imageUrl)
  } catch (error) {
    message.error('头像上传失败')
    options.onError?.(error as Error)
  }
}

const handleSaveNotifications = async () => {
  notificationLoading.value = true
  try {
    // 更新通知设置
    await notificationStore.updateSettings({
      auditCompleted: notificationSettings.email.includes('audit_completed'),
      auditFailed: notificationSettings.email.includes('audit_failed'),
      vulnerabilityFound: notificationSettings.email.includes('vulnerability_found'),
      reportGenerated: notificationSettings.email.includes('report_generated'),
      webhookFailed: notificationSettings.email.includes('webhook_failed'),
      enableDesktop: notificationSettings.desktop.length > 0
    })
    
    message.success('通知设置保存成功')
  } catch (error) {
    message.error('保存通知设置失败')
  } finally {
    notificationLoading.value = false
  }
}

onMounted(() => {
  // 初始化表单数据
  if (userStore.user) {
    Object.assign(profileForm, {
      username: userStore.user.username,
      email: userStore.user.email
    })
  }
  
  // 加载通知设置
  const settings = notificationStore.settings
  if (settings) {
    notificationSettings.email = []
    notificationSettings.desktop = []
    
    if (settings.auditCompleted) {
      notificationSettings.email.push('audit_completed')
      notificationSettings.desktop.push('audit_completed')
    }
    if (settings.vulnerabilityFound) {
      notificationSettings.email.push('vulnerability_found')
      notificationSettings.desktop.push('vulnerability_found')
    }
    if (settings.reportGenerated) {
      notificationSettings.email.push('report_generated')
      notificationSettings.desktop.push('report_generated')
    }
    if (settings.auditFailed) {
      notificationSettings.email.push('audit_failed')
    }
    if (settings.webhookFailed) {
      notificationSettings.email.push('webhook_failed')
    }
  }
})
</script>

<style scoped>
.profile-page {
  padding: 0;
}

.page-header {
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
}

.avatar-section {
  text-align: center;
  padding: 20px 0;
}

.avatar-tip {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-checkbox-group .ant-col) {
  margin-bottom: 8px;
}
</style>
