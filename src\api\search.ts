import api from './index'

export interface SearchParams {
  keyword: string
  type?: 'project' | 'audit' | 'vulnerability' | 'report' | 'all'
  limit?: number
  offset?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface SearchResult {
  id: string
  type: 'project' | 'audit' | 'vulnerability' | 'report'
  title: string
  description: string
  url: string
  severity?: string
  created_at: string
  updated_at: string
  highlight?: {
    title?: string[]
    description?: string[]
    content?: string[]
  }
}

export interface SearchResponse {
  results: SearchResult[]
  total: number
  took: number
  aggregations?: {
    types: Array<{ key: string; count: number }>
    severities: Array<{ key: string; count: number }>
  }
}

export interface SearchSuggestion {
  text: string
  type: string
  count: number
}

export const searchApi = {
  // 全局搜索
  globalSearch: (params: SearchParams) =>
    api.get<SearchResponse>('/api/v1/search/global', { params }),

  // 按类型搜索
  searchByType: (type: string, params: Omit<SearchParams, 'type'>) =>
    api.get<SearchResponse>(`/api/v1/search/${type}`, { params }),

  // 搜索建议
  getSuggestions: (keyword: string) =>
    api.get<SearchSuggestion[]>('/api/v1/search/suggestions', {
      params: { keyword, limit: 10 }
    }),

  // 搜索项目
  searchProjects: (params: Omit<SearchParams, 'type'>) =>
    api.get<SearchResponse>('/api/v1/search/projects', { params }),

  // 搜索审计
  searchAudits: (params: Omit<SearchParams, 'type'>) =>
    api.get<SearchResponse>('/api/v1/search/audits', { params }),

  // 搜索漏洞
  searchVulnerabilities: (params: Omit<SearchParams, 'type'>) =>
    api.get<SearchResponse>('/api/v1/search/vulnerabilities', { params }),

  // 搜索报告
  searchReports: (params: Omit<SearchParams, 'type'>) =>
    api.get<SearchResponse>('/api/v1/search/reports', { params }),

  // 高级搜索
  advancedSearch: (params: {
    keyword?: string
    filters: {
      types?: string[]
      severities?: string[]
      date_range?: {
        start: string
        end: string
      }
      project_ids?: number[]
      audit_ids?: number[]
    }
    sort_by?: string
    sort_order?: 'asc' | 'desc'
    limit?: number
    offset?: number
  }) =>
    api.post<SearchResponse>('/api/v1/search/advanced', params),

  // 保存搜索历史
  saveSearchHistory: (keyword: string, type?: string) =>
    api.post('/api/v1/search/history', { keyword, type }),

  // 获取搜索历史
  getSearchHistory: (limit: number = 10) =>
    api.get<Array<{ keyword: string; type?: string; count: number; last_searched: string }>>('/api/v1/search/history', {
      params: { limit }
    }),

  // 清空搜索历史
  clearSearchHistory: () =>
    api.delete('/api/v1/search/history'),

  // 获取热门搜索
  getPopularSearches: (limit: number = 10) =>
    api.get<Array<{ keyword: string; count: number }>>('/api/v1/search/popular', {
      params: { limit }
    }),

  // 搜索统计
  getSearchStats: () =>
    api.get<{
      total_searches: number
      unique_keywords: number
      avg_results_per_search: number
      most_searched_type: string
    }>('/api/v1/search/stats')
}
