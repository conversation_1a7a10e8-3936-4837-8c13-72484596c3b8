<template>
  <div class="system-settings">
    <div class="page-header">
      <h2>系统设置</h2>
    </div>
    
    <a-row :gutter="[16, 16]">
      <!-- 基本设置 -->
      <a-col :span="24">
        <a-card title="基本设置" :loading="loading">
          <a-form
            ref="basicFormRef"
            :model="basicSettings"
            :rules="basicRules"
            layout="vertical"
            @finish="handleSaveBasic"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="系统名称" name="system_name">
                  <a-input v-model:value="basicSettings.system_name" placeholder="请输入系统名称" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="系统版本" name="system_version">
                  <a-input v-model:value="basicSettings.system_version" placeholder="请输入系统版本" />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="管理员邮箱" name="admin_email">
                  <a-input v-model:value="basicSettings.admin_email" placeholder="请输入管理员邮箱" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="系统时区" name="timezone">
                  <a-select v-model:value="basicSettings.timezone" placeholder="请选择时区">
                    <a-select-option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</a-select-option>
                    <a-select-option value="UTC">UTC (UTC+0)</a-select-option>
                    <a-select-option value="America/New_York">America/New_York (UTC-5)</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-form-item label="系统描述" name="system_description">
              <a-textarea
                v-model:value="basicSettings.system_description"
                placeholder="请输入系统描述"
                :rows="3"
              />
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="saveLoading.basic">
                保存基本设置
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 安全设置 -->
      <a-col :span="24">
        <a-card title="安全设置" :loading="loading">
          <a-form
            ref="securityFormRef"
            :model="securitySettings"
            :rules="securityRules"
            layout="vertical"
            @finish="handleSaveSecurity"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="密码最小长度" name="password_min_length">
                  <a-input-number
                    v-model:value="securitySettings.password_min_length"
                    :min="6"
                    :max="20"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="登录失败锁定次数" name="login_max_attempts">
                  <a-input-number
                    v-model:value="securitySettings.login_max_attempts"
                    :min="3"
                    :max="10"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="会话超时时间（分钟）" name="session_timeout">
                  <a-input-number
                    v-model:value="securitySettings.session_timeout"
                    :min="30"
                    :max="1440"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="锁定时间（分钟）" name="lockout_duration">
                  <a-input-number
                    v-model:value="securitySettings.lockout_duration"
                    :min="5"
                    :max="60"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-form-item label="安全选项">
              <a-checkbox-group v-model:value="securitySettings.security_options">
                <a-row>
                  <a-col :span="8">
                    <a-checkbox value="require_strong_password">强制强密码</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="enable_two_factor">启用双因子认证</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="log_security_events">记录安全事件</a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="saveLoading.security">
                保存安全设置
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 通知设置 -->
      <a-col :span="24">
        <a-card title="通知设置" :loading="loading">
          <a-form
            ref="notificationFormRef"
            :model="notificationSettings"
            layout="vertical"
            @finish="handleSaveNotification"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="SMTP服务器" name="smtp_host">
                  <a-input v-model:value="notificationSettings.smtp_host" placeholder="请输入SMTP服务器地址" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="SMTP端口" name="smtp_port">
                  <a-input-number
                    v-model:value="notificationSettings.smtp_port"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="发送邮箱" name="smtp_username">
                  <a-input v-model:value="notificationSettings.smtp_username" placeholder="请输入发送邮箱" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮箱密码" name="smtp_password">
                  <a-input-password v-model:value="notificationSettings.smtp_password" placeholder="请输入邮箱密码" />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-form-item label="通知选项">
              <a-checkbox-group v-model:value="notificationSettings.notification_options">
                <a-row>
                  <a-col :span="8">
                    <a-checkbox value="enable_email">启用邮件通知</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="enable_webhook">启用Webhook通知</a-checkbox>
                  </a-col>
                  <a-col :span="8">
                    <a-checkbox value="enable_desktop">启用桌面通知</a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit" :loading="saveLoading.notification">
                  保存通知设置
                </a-button>
                <a-button @click="handleTestEmail" :loading="testLoading">
                  测试邮件发送
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 系统信息 -->
      <a-col :span="24">
        <a-card title="系统信息" :loading="loading">
          <a-descriptions :column="2">
            <a-descriptions-item label="系统版本">
              {{ systemInfo.version }}
            </a-descriptions-item>
            <a-descriptions-item label="构建时间">
              {{ systemInfo.build_time }}
            </a-descriptions-item>
            <a-descriptions-item label="运行时间">
              {{ systemInfo.uptime }}
            </a-descriptions-item>
            <a-descriptions-item label="数据库版本">
              {{ systemInfo.database_version }}
            </a-descriptions-item>
            <a-descriptions-item label="Python版本">
              {{ systemInfo.python_version }}
            </a-descriptions-item>
            <a-descriptions-item label="操作系统">
              {{ systemInfo.os_info }}
            </a-descriptions-item>
            <a-descriptions-item label="CPU使用率">
              <a-progress :percent="systemInfo.cpu_usage" size="small" />
            </a-descriptions-item>
            <a-descriptions-item label="内存使用率">
              <a-progress :percent="systemInfo.memory_usage" size="small" />
            </a-descriptions-item>
            <a-descriptions-item label="磁盘使用率">
              <a-progress :percent="systemInfo.disk_usage" size="small" />
            </a-descriptions-item>
            <a-descriptions-item label="活跃用户数">
              {{ systemInfo.active_users }}
            </a-descriptions-item>
          </a-descriptions>
          
          <a-divider />
          
          <a-space>
            <a-button @click="handleRefreshSystemInfo" :loading="refreshLoading">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新系统信息
            </a-button>
            <a-button @click="handleExportLogs">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出系统日志
            </a-button>
            <a-button danger @click="handleClearCache">
              <template #icon>
                <ClearOutlined />
              </template>
              清理系统缓存
            </a-button>
          </a-space>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  DownloadOutlined,
  ClearOutlined
} from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const refreshLoading = ref(false)
const testLoading = ref(false)
const saveLoading = reactive({
  basic: false,
  security: false,
  notification: false
})

const basicFormRef = ref<FormInstance>()
const securityFormRef = ref<FormInstance>()
const notificationFormRef = ref<FormInstance>()

const basicSettings = reactive({
  system_name: 'VulnAuditBox',
  system_version: '1.0.0',
  admin_email: '<EMAIL>',
  timezone: 'Asia/Shanghai',
  system_description: '专业的代码安全审计平台'
})

const securitySettings = reactive({
  password_min_length: 8,
  login_max_attempts: 5,
  session_timeout: 120,
  lockout_duration: 15,
  security_options: ['require_strong_password', 'log_security_events']
})

const notificationSettings = reactive({
  smtp_host: '',
  smtp_port: 587,
  smtp_username: '',
  smtp_password: '',
  notification_options: ['enable_email', 'enable_desktop']
})

const systemInfo = reactive({
  version: '1.0.0',
  build_time: '2024-01-15 10:30:00',
  uptime: '15天 8小时 30分钟',
  database_version: 'PostgreSQL 14.5',
  python_version: 'Python 3.11.0',
  os_info: 'Ubuntu 22.04 LTS',
  cpu_usage: 25,
  memory_usage: 45,
  disk_usage: 60,
  active_users: 12
})

const basicRules = {
  system_name: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  admin_email: [
    { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  timezone: [
    { required: true, message: '请选择时区', trigger: 'change' }
  ]
}

const securityRules = {
  password_min_length: [
    { required: true, message: '请输入密码最小长度', trigger: 'blur' }
  ],
  login_max_attempts: [
    { required: true, message: '请输入登录失败锁定次数', trigger: 'blur' }
  ],
  session_timeout: [
    { required: true, message: '请输入会话超时时间', trigger: 'blur' }
  ],
  lockout_duration: [
    { required: true, message: '请输入锁定时间', trigger: 'blur' }
  ]
}

const handleSaveBasic = async () => {
  saveLoading.basic = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('基本设置保存成功')
  } catch (error) {
    message.error('保存基本设置失败')
  } finally {
    saveLoading.basic = false
  }
}

const handleSaveSecurity = async () => {
  saveLoading.security = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('安全设置保存成功')
  } catch (error) {
    message.error('保存安全设置失败')
  } finally {
    saveLoading.security = false
  }
}

const handleSaveNotification = async () => {
  saveLoading.notification = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('通知设置保存成功')
  } catch (error) {
    message.error('保存通知设置失败')
  } finally {
    saveLoading.notification = false
  }
}

const handleTestEmail = async () => {
  testLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('测试邮件发送成功，请检查收件箱')
  } catch (error) {
    message.error('测试邮件发送失败')
  } finally {
    testLoading.value = false
  }
}

const handleRefreshSystemInfo = async () => {
  refreshLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新系统信息
    systemInfo.cpu_usage = Math.floor(Math.random() * 100)
    systemInfo.memory_usage = Math.floor(Math.random() * 100)
    systemInfo.disk_usage = Math.floor(Math.random() * 100)
    systemInfo.active_users = Math.floor(Math.random() * 50) + 1
    
    message.success('系统信息刷新成功')
  } catch (error) {
    message.error('刷新系统信息失败')
  } finally {
    refreshLoading.value = false
  }
}

const handleExportLogs = async () => {
  try {
    // 模拟导出日志
    const logContent = `[${new Date().toISOString()}] INFO: System logs exported\n`
    const blob = new Blob([logContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.log`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    message.success('系统日志导出成功')
  } catch (error) {
    message.error('导出系统日志失败')
  }
}

const handleClearCache = async () => {
  try {
    // 模拟清理缓存
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('系统缓存清理成功')
  } catch (error) {
    message.error('清理系统缓存失败')
  }
}

const loadSettings = async () => {
  loading.value = true
  try {
    // 模拟API调用加载设置
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    message.error('加载系统设置失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.system-settings {
  padding: 0;
}

.page-header {
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-progress) {
  margin: 0;
}
</style>
