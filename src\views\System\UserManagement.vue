<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <a-space>
        <a-button @click="handleRefresh" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="showCreateModal = true">
          <template #icon>
            <PlusOutlined />
          </template>
          添加用户
        </a-button>
      </a-space>
    </div>
    
    <a-card>
      <div class="user-toolbar">
        <a-space>
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索用户名或邮箱"
            style="width: 200px"
            @pressEnter="handleSearch"
            allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
          
          <a-select
            v-model:value="roleFilter"
            placeholder="角色筛选"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部角色</a-select-option>
            <a-select-option value="admin">管理员</a-select-option>
            <a-select-option value="user">普通用户</a-select-option>
            <a-select-option value="auditor">审计员</a-select-option>
          </a-select>
          
          <a-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-space>
      </div>
      
      <a-table
        :columns="columns"
        :data-source="users"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar :src="record.avatar" :size="32">
              {{ record.username?.charAt(0).toUpperCase() }}
            </a-avatar>
          </template>
          
          <template v-else-if="column.key === 'username'">
            <a @click="handleViewUser(record)">{{ record.username }}</a>
          </template>
          
          <template v-else-if="column.key === 'role'">
            <a-tag :color="getRoleColor(record.role)">
              {{ getRoleText(record.role) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'is_active'">
            <a-switch
              :checked="record.is_active"
              @change="(checked) => handleToggleStatus(record.id, checked)"
              :loading="toggleLoading[record.id]"
            />
          </template>
          
          <template v-else-if="column.key === 'last_login_at'">
            <span v-if="record.last_login_at">
              {{ formatDate(record.last_login_at) }}
            </span>
            <span v-else class="text-gray">从未登录</span>
          </template>
          
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a @click="handleEditUser(record)">编辑</a>
              <a @click="handleResetPassword(record)">重置密码</a>
              <a-popconfirm
                title="确定要删除这个用户吗？"
                @confirm="handleDeleteUser(record.id)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a style="color: #ff4d4f">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 创建/编辑用户模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingUser ? '编辑用户' : '创建用户'"
      @ok="handleSubmitUser"
      @cancel="handleCancelUser"
      :confirm-loading="submitLoading"
      width="600px"
    >
      <a-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username">
              <a-input v-model:value="userForm.username" placeholder="请输入用户名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="userForm.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="角色" name="role">
              <a-select v-model:value="userForm.role" placeholder="请选择角色">
                <a-select-option value="admin">管理员</a-select-option>
                <a-select-option value="user">普通用户</a-select-option>
                <a-select-option value="auditor">审计员</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="is_active">
              <a-switch v-model:checked="userForm.is_active" />
              <span style="margin-left: 8px">
                {{ userForm.is_active ? '启用' : '禁用' }}
              </span>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="密码" name="password" v-if="!editingUser">
          <a-input-password v-model:value="userForm.password" placeholder="请输入密码" />
        </a-form-item>
        
        <a-form-item label="权限" name="permissions">
          <a-checkbox-group v-model:value="userForm.permissions">
            <a-row>
              <a-col :span="8" v-for="permission in availablePermissions" :key="permission.key">
                <a-checkbox :value="permission.key">{{ permission.name }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 用户详情模态框 -->
    <a-modal
      v-model:open="showDetailModal"
      :title="`用户详情 - ${selectedUser?.username}`"
      :footer="null"
      width="800px"
    >
      <a-descriptions :column="2" v-if="selectedUser">
        <a-descriptions-item label="头像">
          <a-avatar :src="selectedUser.avatar" :size="64">
            {{ selectedUser.username?.charAt(0).toUpperCase() }}
          </a-avatar>
        </a-descriptions-item>
        <a-descriptions-item label="用户名">
          {{ selectedUser.username }}
        </a-descriptions-item>
        <a-descriptions-item label="邮箱">
          {{ selectedUser.email }}
        </a-descriptions-item>
        <a-descriptions-item label="角色">
          <a-tag :color="getRoleColor(selectedUser.role)">
            {{ getRoleText(selectedUser.role) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="selectedUser.is_active ? 'green' : 'red'">
            {{ selectedUser.is_active ? '启用' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="最后登录">
          {{ selectedUser.last_login_at ? formatDate(selectedUser.last_login_at) : '从未登录' }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDate(selectedUser.created_at) }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ formatDate(selectedUser.updated_at) }}
        </a-descriptions-item>
        <a-descriptions-item label="权限" :span="2">
          <a-space wrap>
            <a-tag v-for="permission in selectedUser.permissions" :key="permission" color="blue">
              {{ getPermissionName(permission) }}
            </a-tag>
          </a-space>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { formatDate } from '@/utils/date'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, FormInstance } from 'ant-design-vue'

// 模拟用户数据类型
interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: string
  is_active: boolean
  permissions: string[]
  last_login_at?: string
  created_at: string
  updated_at: string
}

const loading = ref(false)
const submitLoading = ref(false)
const toggleLoading = ref<Record<number, boolean>>({})
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const editingUser = ref<User | null>(null)
const selectedUser = ref<User | null>(null)
const userFormRef = ref<FormInstance>()

const searchKeyword = ref('')
const roleFilter = ref('')
const statusFilter = ref<boolean>()

const users = ref<User[]>([])
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const userForm = reactive({
  username: '',
  email: '',
  password: '',
  role: 'user',
  is_active: true,
  permissions: [] as string[]
})

const availablePermissions = [
  { key: 'dashboard:view', name: '仪表板查看' },
  { key: 'project:view', name: '项目查看' },
  { key: 'project:create', name: '项目创建' },
  { key: 'project:edit', name: '项目编辑' },
  { key: 'project:delete', name: '项目删除' },
  { key: 'audit:view', name: '审计查看' },
  { key: 'audit:create', name: '审计创建' },
  { key: 'audit:edit', name: '审计编辑' },
  { key: 'vulnerability:view', name: '漏洞查看' },
  { key: 'vulnerability:edit', name: '漏洞编辑' },
  { key: 'report:view', name: '报告查看' },
  { key: 'report:create', name: '报告生成' },
  { key: 'webhook:view', name: 'Webhook查看' },
  { key: 'webhook:create', name: 'Webhook创建' },
  { key: 'system:settings', name: '系统设置' },
  { key: 'user:manage', name: '用户管理' }
]

const columns: TableColumnsType = [
  {
    title: '头像',
    dataIndex: 'avatar',
    key: 'avatar',
    width: 80
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 120,
    sorter: (a: any, b: any) => a.username.localeCompare(b.username)
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 200
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    width: 100,
    filters: [
      { text: '管理员', value: 'admin' },
      { text: '普通用户', value: 'user' },
      { text: '审计员', value: 'auditor' }
    ],
    onFilter: (value: string, record: any) => record.role === value
  },
  {
    title: '状态',
    dataIndex: 'is_active',
    key: 'is_active',
    width: 80
  },
  {
    title: '最后登录',
    dataIndex: 'last_login_at',
    key: 'last_login_at',
    width: 180,
    sorter: (a: any, b: any) => {
      if (!a.last_login_at && !b.last_login_at) return 0
      if (!a.last_login_at) return 1
      if (!b.last_login_at) return -1
      return new Date(a.last_login_at).getTime() - new Date(b.last_login_at).getTime()
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
    sorter: (a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const getRoleColor = (role: string) => {
  const colorMap: Record<string, string> = {
    admin: 'red',
    user: 'blue',
    auditor: 'green'
  }
  return colorMap[role] || 'default'
}

const getRoleText = (role: string) => {
  const textMap: Record<string, string> = {
    admin: '管理员',
    user: '普通用户',
    auditor: '审计员'
  }
  return textMap[role] || role
}

const getPermissionName = (permission: string) => {
  const perm = availablePermissions.find(p => p.key === permission)
  return perm?.name || permission
}

const handleSearch = () => {
  fetchUsers()
}

const handleRefresh = () => {
  fetchUsers()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchUsers()
}

const handleViewUser = (user: User) => {
  selectedUser.value = user
  showDetailModal.value = true
}

const handleEditUser = (user: User) => {
  editingUser.value = user
  Object.assign(userForm, {
    username: user.username,
    email: user.email,
    role: user.role,
    is_active: user.is_active,
    permissions: [...user.permissions],
    password: ''
  })
  showCreateModal.value = true
}

const handleToggleStatus = async (id: number, is_active: boolean) => {
  toggleLoading.value[id] = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    const user = users.value.find(u => u.id === id)
    if (user) {
      user.is_active = is_active
    }
    message.success(`用户状态${is_active ? '启用' : '禁用'}成功`)
  } catch (error) {
    message.error('更新用户状态失败')
  } finally {
    toggleLoading.value[id] = false
  }
}

const handleSubmitUser = async () => {
  try {
    await userFormRef.value?.validate()
    submitLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingUser.value) {
      // 更新用户
      const index = users.value.findIndex(u => u.id === editingUser.value!.id)
      if (index > -1) {
        users.value[index] = {
          ...users.value[index],
          ...userForm,
          updated_at: new Date().toISOString()
        }
      }
      message.success('用户更新成功')
    } else {
      // 创建用户
      const newUser: User = {
        id: Date.now(),
        ...userForm,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      users.value.unshift(newUser)
      message.success('用户创建成功')
    }
    
    handleCancelUser()
  } catch (error) {
    console.error('Submit user error:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleCancelUser = () => {
  showCreateModal.value = false
  editingUser.value = null
  userFormRef.value?.resetFields()
  Object.assign(userForm, {
    username: '',
    email: '',
    password: '',
    role: 'user',
    is_active: true,
    permissions: []
  })
}

const handleResetPassword = async (user: User) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success(`用户 ${user.username} 的密码重置成功，新密码已发送到邮箱`)
  } catch (error) {
    message.error('重置密码失败')
  }
}

const handleDeleteUser = async (id: number) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    users.value = users.value.filter(u => u.id !== id)
    message.success('用户删除成功')
  } catch (error) {
    message.error('删除用户失败')
  }
}

const fetchUsers = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟用户数据
    const mockUsers: User[] = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        is_active: true,
        permissions: availablePermissions.map(p => p.key),
        last_login_at: new Date().toISOString(),
        created_at: '2024-01-01T00:00:00Z',
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        username: 'auditor1',
        email: '<EMAIL>',
        role: 'auditor',
        is_active: true,
        permissions: ['dashboard:view', 'audit:view', 'audit:create', 'vulnerability:view', 'report:view'],
        last_login_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        created_at: '2024-01-02T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        username: 'user1',
        email: '<EMAIL>',
        role: 'user',
        is_active: false,
        permissions: ['dashboard:view', 'project:view'],
        created_at: '2024-01-03T00:00:00Z',
        updated_at: '2024-01-03T00:00:00Z'
      }
    ]
    
    users.value = mockUsers
    pagination.total = mockUsers.length
  } catch (error) {
    message.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
}

.user-toolbar {
  margin-bottom: 16px;
}

.text-gray {
  color: #999;
}

:deep(.ant-table-cell) {
  padding: 12px 8px;
}

:deep(.ant-switch-loading) {
  opacity: 0.6;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}
</style>
