<template>
  <div class="theme-toggle">
    <!-- 简单切换按钮 -->
    <a-button 
      v-if="mode === 'simple'"
      type="text" 
      shape="circle" 
      size="large"
      @click="toggleTheme"
      :title="themeStore.isDarkMode ? '切换到浅色模式' : '切换到深色模式'"
    >
      <template #icon>
        <SunOutlined v-if="themeStore.isDarkMode" />
        <MoonOutlined v-else />
      </template>
    </a-button>
    
    <!-- 下拉菜单模式 -->
    <a-dropdown v-else-if="mode === 'dropdown'" placement="bottomRight">
      <a-button type="text" shape="circle" size="large">
        <template #icon>
          <SunOutlined v-if="themeStore.currentTheme === 'light'" />
          <MoonOutlined v-else-if="themeStore.currentTheme === 'dark'" />
          <DesktopOutlined v-else />
        </template>
      </a-button>
      
      <template #overlay>
        <a-menu @click="handleMenuClick" :selected-keys="[themeStore.themeConfig.mode]">
          <a-menu-item key="light">
            <SunOutlined />
            浅色模式
          </a-menu-item>
          <a-menu-item key="dark">
            <MoonOutlined />
            深色模式
          </a-menu-item>
          <a-menu-item key="auto">
            <DesktopOutlined />
            跟随系统
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    
    <!-- 完整设置面板 -->
    <div v-else-if="mode === 'panel'" class="theme-panel">
      <a-card title="主题设置" size="small">
        <div class="theme-section">
          <div class="section-title">主题模式</div>
          <a-radio-group 
            v-model:value="themeStore.themeConfig.mode" 
            @change="handleModeChange"
            button-style="solid"
            size="small"
          >
            <a-radio-button value="light">
              <SunOutlined />
              浅色
            </a-radio-button>
            <a-radio-button value="dark">
              <MoonOutlined />
              深色
            </a-radio-button>
            <a-radio-button value="auto">
              <DesktopOutlined />
              自动
            </a-radio-button>
          </a-radio-group>
        </div>
        
        <div class="theme-section">
          <div class="section-title">主色调</div>
          <div class="color-picker">
            <div 
              v-for="color in themeStore.presetColors" 
              :key="color.value"
              class="color-item"
              :class="{ active: themeStore.themeConfig.primaryColor === color.value }"
              :style="{ backgroundColor: color.value }"
              @click="themeStore.setPrimaryColor(color.value)"
              :title="color.name"
            />
            <a-color-picker
              v-model:value="customColor"
              @change="handleCustomColorChange"
              size="small"
              show-text
            />
          </div>
        </div>
        
        <div class="theme-section">
          <div class="section-title">圆角大小</div>
          <a-slider
            v-model:value="themeStore.themeConfig.borderRadius"
            :min="0"
            :max="16"
            :step="1"
            @change="themeStore.setBorderRadius"
            :tooltip-formatter="(value: number) => `${value}px`"
          />
        </div>
        
        <div class="theme-section">
          <div class="section-title">其他设置</div>
          <div class="theme-options">
            <a-checkbox 
              v-model:checked="themeStore.themeConfig.compactMode"
              @change="themeStore.toggleCompactMode"
            >
              紧凑模式
            </a-checkbox>
            <a-checkbox 
              v-model:checked="themeStore.themeConfig.colorWeakMode"
              @change="themeStore.toggleColorWeakMode"
            >
              色弱模式
            </a-checkbox>
          </div>
        </div>
        
        <div class="theme-actions">
          <a-space>
            <a-button size="small" @click="handleExport">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </a-button>
            <a-button size="small" @click="showImportModal = true">
              <template #icon>
                <UploadOutlined />
              </template>
              导入
            </a-button>
            <a-button size="small" @click="themeStore.resetThemeConfig">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-space>
        </div>
      </a-card>
    </div>
    
    <!-- 导入配置模态框 -->
    <a-modal
      v-model:open="showImportModal"
      title="导入主题配置"
      @ok="handleImport"
      @cancel="importConfig = ''"
    >
      <a-textarea
        v-model:value="importConfig"
        placeholder="请粘贴主题配置JSON"
        :rows="10"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useThemeStore } from '@/stores/themeStore'
import { message } from 'ant-design-vue'
import {
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  DownloadOutlined,
  UploadOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import type { ThemeMode } from '@/stores/themeStore'

interface Props {
  mode?: 'simple' | 'dropdown' | 'panel'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'simple'
})

const themeStore = useThemeStore()
const showImportModal = ref(false)
const importConfig = ref('')
const customColor = ref(themeStore.themeConfig.primaryColor)

// 简单切换主题
const toggleTheme = () => {
  const newMode: ThemeMode = themeStore.isDarkMode ? 'light' : 'dark'
  themeStore.setThemeMode(newMode)
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  themeStore.setThemeMode(key as ThemeMode)
}

// 处理模式变化
const handleModeChange = (e: any) => {
  themeStore.setThemeMode(e.target.value)
}

// 处理自定义颜色变化
const handleCustomColorChange = (color: string) => {
  themeStore.setPrimaryColor(color)
}

// 导出配置
const handleExport = () => {
  try {
    const config = themeStore.exportThemeConfig()
    const blob = new Blob([config], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `theme-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    message.success('主题配置导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

// 导入配置
const handleImport = () => {
  if (!importConfig.value.trim()) {
    message.warning('请输入配置内容')
    return
  }
  
  const success = themeStore.importThemeConfig(importConfig.value)
  if (success) {
    message.success('主题配置导入成功')
    showImportModal.value = false
    importConfig.value = ''
  } else {
    message.error('配置格式错误，导入失败')
  }
}
</script>

<style scoped>
.theme-toggle {
  display: inline-block;
}

.theme-panel {
  width: 280px;
  padding: 16px;
}

.theme-section {
  margin-bottom: 20px;
}

.theme-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--ant-color-text);
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.color-item:hover {
  transform: scale(1.1);
}

.color-item.active {
  border-color: var(--ant-color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.theme-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.theme-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--ant-color-border);
}

/* 暗黑模式适配 */
[data-theme='dark'] .section-title {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .theme-actions {
  border-top-color: #303030;
}

/* 响应式 */
@media (max-width: 768px) {
  .theme-panel {
    width: 100%;
    max-width: 320px;
  }
}
</style>
