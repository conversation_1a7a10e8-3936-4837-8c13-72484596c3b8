import api from './index'
import type {
  Project,
  CreateProjectForm,
  UpdateProjectForm,
  ProjectQueryParams,
  GitRepositoryInfo
} from '@/types/project'

export const projectApi = {
  // 获取项目列表
  getProjects: (params?: ProjectQueryParams) =>
    api.get<Project[]>('/projects/', { params }),

  // 获取项目详情
  getProject: (id: number) =>
    api.get<Project>(`/projects/${id}`),

  // 创建项目
  createProject: (data: CreateProjectForm) =>
    api.post<Project>('/projects/', data),

  // 更新项目
  updateProject: (id: number, data: UpdateProjectForm) =>
    api.put<Project>(`/projects/${id}`, data),

  // 删除项目
  deleteProject: (id: number) =>
    api.delete(`/projects/${id}`),

  // 更新项目扩展数据
  updateProjectExtraData: (id: number, extra_data: Record<string, any>) =>
    api.put<Project>(`/projects/${id}/extra-data`, { extra_data }),

  // 获取Git仓库信息
  getGitInfo: (id: number) =>
    api.get<GitRepositoryInfo>(`/projects/${id}/git-info`),

  // 刷新Git哈希
  refreshGitHash: (id: number) =>
    api.post<Project>(`/projects/${id}/refresh-hash`),
}
