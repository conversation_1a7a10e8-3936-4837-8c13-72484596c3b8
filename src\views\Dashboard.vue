<template>
  <div class="dashboard">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <a-card :bordered="false" class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ userStore.user?.username || '用户' }}！</h2>
            <p>今天是 {{ formatDate(new Date(), 'YYYY年MM月DD日') }}，让我们开始新的安全审计工作。</p>
          </div>
          <div class="welcome-actions">
            <a-space>
              <a-button type="primary" @click="$router.push('/audit/create')">
                <template #icon>
                  <PlusOutlined />
                </template>
                创建审计
              </a-button>
              <a-button @click="$router.push('/project/create')">
                <template #icon>
                  <ProjectOutlined />
                </template>
                新建项目
              </a-button>
            </a-space>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="stats-row">
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card" :loading="loading">
          <a-statistic
            title="项目总数"
            :value="dashboardStats.totalProjects"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <ProjectOutlined />
            </template>
          </a-statistic>
          <div class="stat-trend">
            <span class="trend-text">
              较上月
              <span :class="getTrendClass(dashboardStats.projectTrend)">
                {{ dashboardStats.projectTrend > 0 ? '+' : '' }}{{ dashboardStats.projectTrend }}%
              </span>
            </span>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card" :loading="loading">
          <a-statistic
            title="审计次数"
            :value="dashboardStats.totalAudits"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <BugOutlined />
            </template>
          </a-statistic>
          <div class="stat-trend">
            <span class="trend-text">
              较上月
              <span :class="getTrendClass(dashboardStats.auditTrend)">
                {{ dashboardStats.auditTrend > 0 ? '+' : '' }}{{ dashboardStats.auditTrend }}%
              </span>
            </span>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card" :loading="loading">
          <a-statistic
            title="发现漏洞"
            :value="dashboardStats.totalVulnerabilities"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <ExclamationCircleOutlined />
            </template>
          </a-statistic>
          <div class="stat-trend">
            <span class="trend-text">
              较上月
              <span :class="getTrendClass(-dashboardStats.vulnerabilityTrend)">
                {{ dashboardStats.vulnerabilityTrend > 0 ? '+' : '' }}{{ dashboardStats.vulnerabilityTrend }}%
              </span>
            </span>
          </div>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :lg="6">
        <a-card class="stat-card" :loading="loading">
          <a-statistic
            title="风险评分"
            :value="dashboardStats.riskScore"
            suffix="/ 100"
            :value-style="{ color: getRiskColor(dashboardStats.riskScore) }"
          >
            <template #prefix>
              <SafetyCertificateOutlined />
            </template>
          </a-statistic>
          <div class="stat-trend">
            <a-progress
              :percent="dashboardStats.riskScore"
              :stroke-color="getRiskColor(dashboardStats.riskScore)"
              size="small"
              :show-info="false"
            />
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" class="charts-row">
      <!-- 审计趋势图表 -->
      <a-col :xs="24" :lg="12">
        <a-card title="审计趋势" :bordered="false" :loading="loading">
          <template #extra>
            <a-select v-model:value="auditTrendPeriod" size="small" style="width: 100px">
              <a-select-option value="7d">7天</a-select-option>
              <a-select-option value="30d">30天</a-select-option>
              <a-select-option value="90d">90天</a-select-option>
            </a-select>
          </template>
          <LineChart
            :data="auditTrendData"
            :loading="loading"
            title="审计趋势"
            x-axis-name="日期"
            y-axis-name="审计次数"
            :smooth="true"
            :area-style="true"
            height="300px"
          />
        </a-card>
      </a-col>

      <!-- 漏洞分布图表 -->
      <a-col :xs="24" :lg="12">
        <a-card title="漏洞严重程度分布" :bordered="false" :loading="loading">
          <template #extra>
            <a-tooltip title="点击图例可以切换显示">
              <InfoCircleOutlined />
            </a-tooltip>
          </template>
          <PieChart
            :data="vulnerabilityDistributionData"
            :loading="loading"
            title="漏洞严重程度分布"
            :show-legend="true"
            :radius="['40%', '70%']"
            height="300px"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 项目状态和活动图表 -->
    <a-row :gutter="[16, 16]" class="charts-row">
      <!-- 项目状态分布 -->
      <a-col :xs="24" :lg="8">
        <a-card title="项目状态分布" :bordered="false" :loading="loading">
          <PieChart
            :data="projectStatusData"
            :loading="loading"
            title="项目状态"
            :show-legend="false"
            :show-label="true"
            height="200px"
          />
        </a-card>
      </a-col>

      <!-- 月度活动统计 -->
      <a-col :xs="24" :lg="16">
        <a-card title="月度活动统计" :bordered="false" :loading="loading">
          <BarChart
            :data="monthlyActivityData"
            :loading="loading"
            title="月度活动统计"
            x-axis-name="月份"
            y-axis-name="数量"
            height="300px"
          />
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 最近活动和快速操作 -->
    <a-row :gutter="[16, 16]" class="activity-row">
      <!-- 最近项目 -->
      <a-col :xs="24" :lg="8">
        <a-card title="最近项目" :bordered="false" :loading="loading">
          <template #extra>
            <a @click="$router.push('/project/list')">查看全部</a>
          </template>
          <a-list
            :data-source="recentProjects"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta
                  :title="item.name"
                  :description="item.description"
                >
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: getRandomColor() }" size="small">
                      {{ item.name.charAt(0).toUpperCase() }}
                    </a-avatar>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a @click="$router.push(`/project/detail/${item.id}`)">查看</a>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <!-- 最近审计 -->
      <a-col :xs="24" :lg="8">
        <a-card title="最近审计" :bordered="false" :loading="loading">
          <template #extra>
            <a @click="$router.push('/audit/list')">查看全部</a>
          </template>
          <a-list
            :data-source="recentAudits"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta
                  :title="item.projectName"
                  :description="`发现 ${item.vulnerabilityCount} 个漏洞`"
                >
                  <template #avatar>
                    <a-badge
                      :status="getAuditStatus(item.status)"
                      :text="getAuditStatusText(item.status)"
                    />
                  </template>
                </a-list-item-meta>
                <div class="audit-date">{{ formatDate(item.completedAt) }}</div>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <!-- 快速操作 -->
      <a-col :xs="24" :lg="8">
        <a-card title="快速操作" :bordered="false">
          <div class="quick-actions">
            <a-row :gutter="[8, 8]">
              <a-col :span="12">
                <a-button block @click="$router.push('/project/create')">
                  <template #icon>
                    <PlusOutlined />
                  </template>
                  新建项目
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button block @click="$router.push('/audit/create')">
                  <template #icon>
                    <BugOutlined />
                  </template>
                  创建审计
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button block @click="$router.push('/vulnerability/list')">
                  <template #icon>
                    <ExclamationCircleOutlined />
                  </template>
                  查看漏洞
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button block @click="$router.push('/report/generate')">
                  <template #icon>
                    <FileTextOutlined />
                  </template>
                  生成报告
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button block @click="$router.push('/webhook/create')">
                  <template #icon>
                    <ApiOutlined />
                  </template>
                  创建Webhook
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button block @click="refreshDashboard">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  刷新数据
                </a-button>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useProjectStore } from '@/stores/project'
import { useAuditStore } from '@/stores/audit'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import {
  ProjectOutlined,
  BugOutlined,
  ExclamationCircleOutlined,
  SafetyCertificateOutlined,
  PlusOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  ApiOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { formatDate, getRandomColor } from '@/utils/common'
import LineChart from '@/components/Charts/LineChart.vue'
import PieChart from '@/components/Charts/PieChart.vue'
import BarChart from '@/components/Charts/BarChart.vue'
import dayjs from 'dayjs'

const userStore = useUserStore()
const projectStore = useProjectStore()
const auditStore = useAuditStore()
const vulnerabilityStore = useVulnerabilityStore()

const loading = ref(false)
const auditTrendPeriod = ref('30d')

// 仪表板统计数据
const dashboardStats = ref({
  totalProjects: 0,
  totalAudits: 0,
  totalVulnerabilities: 0,
  riskScore: 0,
  projectTrend: 0,
  auditTrend: 0,
  vulnerabilityTrend: 0
})

// 最近项目和审计数据
const recentProjects = computed(() => projectStore.projects.slice(0, 5))
const recentAudits = computed(() => auditStore.audits.slice(0, 5))

// 图表数据
const auditTrendData = computed(() => {
  const days = auditTrendPeriod.value === '7d' ? 7 : auditTrendPeriod.value === '30d' ? 30 : 90
  const data = Array.from({ length: days }, (_, i) => ({
    name: dayjs().subtract(days - 1 - i, 'day').format('MM/DD'),
    value: Math.floor(Math.random() * 10) + 1
  }))

  return [{
    name: '审计次数',
    data,
    color: '#1890ff',
    smooth: true,
    areaStyle: true
  }]
})

const vulnerabilityDistributionData = computed(() => [
  { name: '严重', value: Math.floor(dashboardStats.value.totalVulnerabilities * 0.1), color: '#f5222d' },
  { name: '高危', value: Math.floor(dashboardStats.value.totalVulnerabilities * 0.2), color: '#fa541c' },
  { name: '中危', value: Math.floor(dashboardStats.value.totalVulnerabilities * 0.3), color: '#faad14' },
  { name: '低危', value: Math.floor(dashboardStats.value.totalVulnerabilities * 0.4), color: '#52c41a' }
])

const projectStatusData = computed(() => [
  { name: '活跃', value: Math.floor(dashboardStats.value.totalProjects * 0.6), color: '#52c41a' },
  { name: '暂停', value: Math.floor(dashboardStats.value.totalProjects * 0.3), color: '#faad14' },
  { name: '已完成', value: Math.floor(dashboardStats.value.totalProjects * 0.1), color: '#1890ff' }
])

const monthlyActivityData = computed(() => {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月']
  return [
    {
      name: '项目',
      data: months.map(month => ({ name: month, value: Math.floor(Math.random() * 20) + 5 })),
      color: '#1890ff'
    },
    {
      name: '审计',
      data: months.map(month => ({ name: month, value: Math.floor(Math.random() * 30) + 10 })),
      color: '#52c41a'
    },
    {
      name: '漏洞',
      data: months.map(month => ({ name: month, value: Math.floor(Math.random() * 50) + 20 })),
      color: '#faad14'
    }
  ]
})









// 工具函数
const getRiskColor = (score: number) => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  if (score >= 40) return '#fa541c'
  return '#f5222d'
}

const getTrendClass = (trend: number) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-neutral'
}

const getAuditStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    completed: 'success',
    running: 'processing',
    failed: 'error',
    pending: 'default'
  }
  return statusMap[status] || 'default'
}

const getAuditStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    completed: '已完成',
    running: '进行中',
    failed: '失败',
    pending: '等待中'
  }
  return statusMap[status] || '未知'
}

// 加载仪表板数据
const loadDashboardData = async () => {
  loading.value = true
  try {
    // 并行加载各种数据
    await Promise.all([
      projectStore.fetchProjects({ limit: 10 }),
      auditStore.fetchAudits({ limit: 10 }),
      vulnerabilityStore.fetchStatistics()
    ])

    // 更新统计数据
    dashboardStats.value = {
      totalProjects: projectStore.total,
      totalAudits: auditStore.total,
      totalVulnerabilities: vulnerabilityStore.statistics?.total || 0,
      riskScore: calculateRiskScore(),
      projectTrend: Math.floor(Math.random() * 20) - 10, // 模拟趋势数据
      auditTrend: Math.floor(Math.random() * 30) - 15,
      vulnerabilityTrend: Math.floor(Math.random() * 25) - 12
    }
  } catch (error) {
    console.error('Load dashboard data error:', error)
  } finally {
    loading.value = false
  }
}

// 计算风险评分
const calculateRiskScore = () => {
  const stats = vulnerabilityStore.statistics
  if (!stats) return 0

  const total = stats.total
  if (total === 0) return 100

  const criticalWeight = stats.by_severity?.critical || 0
  const highWeight = stats.by_severity?.high || 0
  const mediumWeight = stats.by_severity?.medium || 0
  const lowWeight = stats.by_severity?.low || 0

  const weightedScore = (criticalWeight * 10 + highWeight * 7 + mediumWeight * 4 + lowWeight * 1)
  const maxPossibleScore = total * 10

  return Math.max(0, Math.min(100, 100 - Math.floor((weightedScore / maxPossibleScore) * 100)))
}

// 刷新仪表板
const refreshDashboard = () => {
  loadDashboardData()
}

// 定时刷新
let refreshInterval: NodeJS.Timeout | null = null

onMounted(() => {
  loadDashboardData()

  // 每5分钟自动刷新一次
  refreshInterval = setInterval(() => {
    loadDashboardData()
  }, 5 * 60 * 1000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card :deep(.ant-card-body) {
  padding: 24px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  color: white;
  margin: 0 0 8px 0;
  font-size: 24px;
}

.welcome-text p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-trend {
  margin-top: 8px;
  font-size: 12px;
}

.trend-text {
  color: #666;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #f5222d;
}

.trend-neutral {
  color: #666;
}

.charts-row {
  margin-bottom: 24px;
}

.chart {
  height: 300px;
}

.chart-small {
  height: 200px;
}

.activity-row {
  margin-bottom: 24px;
}

.audit-date {
  font-size: 12px;
  color: #666;
}

.quick-actions {
  padding: 8px 0;
}

.quick-actions .ant-btn {
  height: 40px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

:deep(.ant-statistic-title) {
  margin-bottom: 8px;
  font-size: 14px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.ant-list-item-meta-title) {
  font-size: 14px;
}

:deep(.ant-list-item-meta-description) {
  font-size: 12px;
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }

  .welcome-actions {
    margin-top: 16px;
  }

  .chart,
  .chart-small {
    height: 250px;
  }
}
</style>
