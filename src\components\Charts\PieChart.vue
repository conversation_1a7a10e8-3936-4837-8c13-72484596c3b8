<template>
  <BaseChart
    :option="chartOption"
    :data="data"
    :loading="loading"
    :error="error"
    :width="width"
    :height="height"
    @retry="$emit('retry')"
    @chart-ready="$emit('chartReady', $event)"
    @chart-click="$emit('chartClick', $event)"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'
import type { EChartsOption } from 'echarts'

interface DataItem {
  name: string
  value: number
  color?: string
}

interface Props {
  data: DataItem[]
  loading?: boolean
  error?: string
  width?: string
  height?: string
  title?: string
  subtitle?: string
  showLegend?: boolean
  showLabel?: boolean
  radius?: string | [string, string]
  center?: [string, string]
  roseType?: boolean | 'radius' | 'area'
  labelPosition?: 'inside' | 'outside'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: '',
  width: '100%',
  height: '400px',
  showLegend: true,
  showLabel: true,
  radius: '70%',
  center: ['50%', '50%'],
  roseType: false,
  labelPosition: 'outside'
})

const emit = defineEmits<{
  retry: []
  chartReady: [chart: any]
  chartClick: [params: any]
}>()

const chartOption = computed((): EChartsOption => {
  if (!props.data || props.data.length === 0) {
    return {}
  }

  // 处理数据，添加默认颜色
  const processedData = props.data.map((item, index) => ({
    ...item,
    itemStyle: {
      color: item.color || getDefaultColor(index)
    }
  }))

  return {
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'left',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      },
      subtextStyle: {
        fontSize: 12
      }
    } : undefined,
    
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percent = params.percent.toFixed(1)
        return `${params.marker}${params.name}<br/>数量: ${params.value}<br/>占比: ${percent}%`
      }
    },
    
    legend: props.showLegend ? {
      orient: 'vertical',
      left: 'right',
      top: 'middle',
      type: 'scroll',
      data: props.data.map(item => item.name),
      formatter: (name: string) => {
        const item = props.data.find(d => d.name === name)
        return item ? `${name} (${item.value})` : name
      }
    } : undefined,
    
    series: [{
      type: 'pie',
      radius: props.radius,
      center: props.center,
      data: processedData,
      roseType: props.roseType,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: props.showLabel ? {
        show: true,
        position: props.labelPosition,
        formatter: (params: any) => {
          if (props.labelPosition === 'inside') {
            return params.percent >= 5 ? `${params.percent.toFixed(1)}%` : ''
          }
          return `${params.name}\n${params.value} (${params.percent.toFixed(1)}%)`
        },
        fontSize: 12
      } : {
        show: false
      },
      labelLine: props.showLabel && props.labelPosition === 'outside' ? {
        show: true,
        length: 15,
        length2: 10
      } : {
        show: false
      }
    }],
    
    animation: true,
    animationType: 'scale',
    animationEasing: 'elasticOut',
    animationDelay: (idx: number) => Math.random() * 200
  }
})

// 获取默认颜色
const getDefaultColor = (index: number): string => {
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', 
    '#722ed1', '#13c2c2', '#eb2f96', '#fa541c',
    '#2f54eb', '#fa8c16', '#a0d911', '#ff7a45'
  ]
  return colors[index % colors.length]
}
</script>
