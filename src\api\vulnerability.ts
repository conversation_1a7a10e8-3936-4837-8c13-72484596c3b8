import api from './index'
import type { 
  Vulnerability,
  VulnerabilityCreate,
  VulnerabilityUpdate,
  VulnerabilityUpdateRequest,
  VulnerabilityBulkUpdate,
  VulnerabilityQueryParams,
  VulnerabilityFilterParams,
  VulnerabilityStatsResponse
} from '@/types/vulnerability'

export const vulnerabilityApi = {
  // 获取漏洞列表
  getVulnerabilities: (params?: VulnerabilityQueryParams) => 
    api.get<Vulnerability[]>('/vulnerabilities/', { params }),

  // 获取过滤后的漏洞列表
  getVulnerabilitiesFiltered: (params?: VulnerabilityFilterParams) => 
    api.get<Vulnerability[]>('/vulnerabilities/filter', { params }),

  // 获取漏洞详情
  getVulnerability: (id: number) =>
    api.get<Vulnerability>(`/vulnerabilities/${id}`),

  // 创建漏洞
  createVulnerability: (data: VulnerabilityCreate) =>
    api.post<Vulnerability>('/vulnerabilities/', data),

  // 更新漏洞状态
  updateVulnerabilityStatus: (id: number, data: VulnerabilityUpdateRequest) =>
    api.put<Vulnerability>(`/vulnerabilities/${id}/status`, data),

  // 更新漏洞信息
  updateVulnerability: (id: number, data: VulnerabilityUpdate) =>
    api.put<Vulnerability>(`/vulnerabilities/${id}`, data),

  // 删除漏洞
  deleteVulnerability: (id: number) =>
    api.delete(`/vulnerabilities/${id}`),

  // 批量更新漏洞
  bulkUpdateVulnerabilities: (data: VulnerabilityBulkUpdate) =>
    api.post<{ updated_count: number; message: string }>('/vulnerabilities/bulk-update', data),

  // 获取漏洞统计
  getVulnerabilityStats: () =>
    api.get<VulnerabilityStatsResponse>('/vulnerabilities/stats/summary'),

  // 导出漏洞数据
  exportVulnerabilities: (params?: VulnerabilityFilterParams, format: 'csv' | 'json' | 'pdf' = 'csv') =>
    api.get('/vulnerabilities/export', {
      params: { ...params, format },
      responseType: 'blob'
    }),
}
